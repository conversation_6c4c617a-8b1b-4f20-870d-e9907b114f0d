import { useState, useEffect, useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import toast from "react-hot-toast";

import {
  getIntegration,
  updateSelectedSocialAuth,
  unlinkIntegration,
} from "../services/socialAuth";

import {
  buildFacebookOAuthUrl,
  validateBotRequestId,
  validateAccountData,
  handleFacebookAuthError,
} from "../utils/facebookAuth";

/**
 * Custom hook for Facebook/Instagram authentication management
 * @param {string} botRequestId - The bot request ID
 * @returns {object} Hook state and methods
 */
export const useFacebookAuth = (botRequestId) => {
  const { t } = useTranslation();

  // State management
  const [socialAuths, setSocialAuths] = useState([]);
  const [selectedAccountOption, setSelectedAccountOption] = useState(null);
  const [accountOptions, setAccountOptions] = useState([]);
  const [linkedAccount, setLinkedAccount] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState(null);
  const [errorType, setErrorType] = useState("general");

  // Memoized filtered chat account options
  const filteredChatAccountOptions = useMemo(() => {
    if (!accountOptions) return [];
    return accountOptions.filter((option) => option && option.type === "chat");
  }, [accountOptions]);

  // Determine error type based on error details
  const determineErrorType = useCallback((error) => {
    if (!error) return "general";

    const errorMessage = error.message || error.toString().toLowerCase();

    if (errorMessage.includes("network") || errorMessage.includes("fetch")) {
      return "network";
    }
    if (
      errorMessage.includes("unauthorized") ||
      errorMessage.includes("auth")
    ) {
      return "authentication";
    }
    if (errorMessage.includes("timeout")) {
      return "timeout";
    }
    if (
      errorMessage.includes("rate limit") ||
      errorMessage.includes("too many")
    ) {
      return "rateLimit";
    }
    if (errorMessage.includes("config") || errorMessage.includes("client_id")) {
      return "configuration";
    }

    return "general";
  }, []);

  // Set error with type determination
  const setErrorWithType = useCallback(
    (error) => {
      const type = determineErrorType(error);
      setError(error?.message || error);
      setErrorType(type);
    },
    [determineErrorType]
  );

  // Validate botRequestId on mount
  useEffect(() => {
    const validation = validateBotRequestId(botRequestId);
    if (!validation.isValid) {
      setErrorWithType(
        new Error(`Invalid bot request ID: ${validation.errors.join(", ")}`)
      );
      console.error("Invalid botRequestId:", validation.errors);
    }
  }, [botRequestId, setErrorWithType]);

  /**
   * Load integrations from API
   */
  const loadIntegration = useCallback(async () => {
    if (!botRequestId) return;

    setIsLoading(true);
    setError(null);

    try {
      const integrations = await getIntegration();
      setSocialAuths(integrations || []);

      if (integrations && integrations?.socialAuthAccount?.length > 0) {
        // Validate and map account options
        const validAccounts = integrations.socialAuthAccount.filter((auth) => {
          const validation = validateAccountData(auth);
          if (!validation.isValid) {
            console.warn("Invalid account data:", validation.errors, auth);
            return false;
          }
          return true;
        });

        const options = validAccounts.map((auth) => ({
          label: `${auth.accountName} (${auth.accountId})`,
          value: auth.id,
          account: auth,
        }));

        setAccountOptions(options);

        // Find currently selected auth for this bot
        const currentSelectedAuth = validAccounts.filter(
          (auth) =>
            auth.selectedBotCompany &&
            auth.selectedBotCompany.botRequestId === botRequestId
        );

        if (currentSelectedAuth.length > 0) {
          setLinkedAccount(currentSelectedAuth);
        }
      } else {
        // Reset state when no integrations
        setSocialAuths([]);
        setAccountOptions([]);
        setSelectedAccountOption(null);
        setLinkedAccount([]);
      }
    } catch (error) {
      const errorResponse = handleFacebookAuthError(error, "Load Integration");
      setErrorWithType(error);
      toast.error(t("facebookIntegration.errorLoadingIntegration"));

      // Reset state on error
      setSocialAuths([]);
      setAccountOptions([]);
      setSelectedAccountOption(null);
      setLinkedAccount([]);
    } finally {
      setIsLoading(false);
    }
  }, [botRequestId, t]);

  /**
   * Handle account selection change
   */
  const handleAccountSelectionChange = useCallback(
    async (selectedOption, isInitialSelection = false) => {
      if (!selectedOption || !selectedOption.value) return;

      setIsLoading(true);
      setError(null);

      try {
        // Validate account data
        const validation = validateAccountData(selectedOption.account);
        if (!validation.isValid) {
          throw new Error(
            `Invalid account data: ${validation.errors.join(", ")}`
          );
        }

        if (
          !isInitialSelection ||
          (selectedOption.account &&
            selectedOption.account.selectedBotCompany &&
            selectedOption.account.selectedBotCompany.botRequestId ===
              botRequestId)
        ) {
          await updateSelectedSocialAuth({
            socialAuthId: selectedOption.value,
            botRequestId,
          });
          toast.success(t("facebookIntegration.activeAccountUpdated"));
        }

        setSelectedAccountOption(selectedOption);
        setLinkedAccount((prev) => {
          const existingAccount = prev.find(
            (account) => account.id === selectedOption.value
          );
          if (existingAccount) {
            return prev;
          }
          return [...prev, selectedOption.account];
        });
      } catch (error) {
        const errorResponse = handleFacebookAuthError(
          error,
          "Update Selected Account"
        );
        setErrorWithType(error);
        toast.error(t("facebookIntegration.errorUpdatingActiveAccount"));
      } finally {
        setIsLoading(false);
      }
    },
    [botRequestId, t]
  );

  /**
   * Handle Facebook login initiation
   */
  const handleFacebookLogin = useCallback(() => {
    setIsConnecting(true);
    setError(null);

    try {
      const urlResult = buildFacebookOAuthUrl(botRequestId);

      if (!urlResult.success) {
        throw new Error(urlResult.error);
      }

      // Open Facebook OAuth in same window
      window.open(urlResult.url, "_self");
    } catch (error) {
      const errorResponse = handleFacebookAuthError(error, "Facebook Login");
      setErrorWithType(error);
      toast.error(t("facebookIntegration.errorLoadingIntegration"));
      setIsConnecting(false);
    }
  }, [botRequestId, t]);

  /**
   * Handle integration unlinking
   */
  const handleUnlinkIntegration = useCallback(
    async (integrationId) => {
      if (!integrationId) return;

      setIsLoading(true);
      setError(null);

      try {
        await unlinkIntegration(integrationId);
        toast.success(t("facebookIntegration.integrationUnlinked"));

        // Update local state
        setLinkedAccount((prev) =>
          prev.filter((account) => account.id !== integrationId)
        );

        // Reload integrations to get updated state
        await loadIntegration();
      } catch (error) {
        const errorResponse = handleFacebookAuthError(
          error,
          "Unlink Integration"
        );
        setErrorWithType(error);
        toast.error(t("facebookIntegration.errorUnlinkingIntegration"));
      } finally {
        setIsLoading(false);
      }
    },
    [t, loadIntegration]
  );

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Refresh integrations
   */
  const refreshIntegrations = useCallback(() => {
    return loadIntegration();
  }, [loadIntegration]);

  // Load integrations on mount and when botRequestId changes
  useEffect(() => {
    loadIntegration();
  }, [loadIntegration]);

  // Return hook interface
  return {
    // State
    socialAuths,
    selectedAccountOption,
    accountOptions,
    linkedAccount,
    filteredChatAccountOptions,
    isLoading,
    isConnecting,
    error,
    errorType,

    // Actions
    handleAccountSelectionChange,
    handleFacebookLogin,
    handleUnlinkIntegration,
    clearError,
    refreshIntegrations,

    // Computed values
    hasIntegrations: socialAuths.length > 0 || accountOptions.length > 0,
    hasChatAccounts: filteredChatAccountOptions.length > 0,
    hasLinkedAccounts: linkedAccount.length > 0,
  };
};
