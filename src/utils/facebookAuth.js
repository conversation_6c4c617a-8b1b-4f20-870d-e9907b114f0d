import { FACEBOOK_CONFIG, validateFacebookConfig } from "../utility/constants";

/**
 * Validates botRequestId parameter
 * @param {string} botRequestId - The bot request ID to validate
 * @returns {object} Validation result with isValid boolean and errors array
 */
export const validateBotRequestId = (botRequestId) => {
  const errors = [];

  if (!botRequestId) {
    errors.push("botRequestId is required");
  }

  if (typeof botRequestId !== "string") {
    errors.push("botRequestId must be a string");
  }

  if (botRequestId && botRequestId.trim().length === 0) {
    errors.push("botRequestId cannot be empty");
  }

  // Basic format validation (assuming UUID-like format)
  if (botRequestId && !/^[a-zA-Z0-9-_]+$/.test(botRequestId)) {
    errors.push("botRequestId contains invalid characters");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Sanitizes URL parameters to prevent XSS and injection attacks
 * @param {string} param - Parameter to sanitize
 * @returns {string} Sanitized parameter
 */
export const sanitizeUrlParam = (param) => {
  if (!param || typeof param !== "string") {
    return "";
  }

  // Remove potentially dangerous characters
  return param
    .replace(/[<>'"&]/g, "") // Remove HTML/XML characters
    .replace(/[(){}[\]]/g, "") // Remove brackets
    .replace(/[;|&$`]/g, "") // Remove shell injection characters
    .trim();
};

/**
 * Builds Facebook OAuth URL with proper validation and sanitization
 * @param {string} botRequestId - The bot request ID for state parameter
 * @returns {object} Result with url string or error information
 */
export const buildFacebookOAuthUrl = (botRequestId) => {
  // Validate configuration first
  const configValidation = validateFacebookConfig();
  if (!configValidation.isValid) {
    return {
      success: false,
      error: "Facebook configuration is invalid",
      details: configValidation.errors,
    };
  }

  // Validate botRequestId
  const botIdValidation = validateBotRequestId(botRequestId);
  if (!botIdValidation.isValid) {
    return {
      success: false,
      error: "Invalid botRequestId",
      details: botIdValidation.errors,
    };
  }

  // Sanitize the botRequestId for use in URL
  const sanitizedBotRequestId = sanitizeUrlParam(botRequestId);

  try {
    // Build URL parameters
    const params = new URLSearchParams({
      client_id: FACEBOOK_CONFIG.CLIENT_ID,
      display: FACEBOOK_CONFIG.OAUTH_CONFIG.display,
      extras: FACEBOOK_CONFIG.OAUTH_CONFIG.extras,
      redirect_uri: FACEBOOK_CONFIG.CALLBACK_URL,
      response_type: FACEBOOK_CONFIG.OAUTH_CONFIG.response_type,
      scope: FACEBOOK_CONFIG.SCOPES.join(","),
      state: sanitizedBotRequestId,
    });

    const oauthUrl = `${FACEBOOK_CONFIG.OAUTH_BASE_URL}/${
      FACEBOOK_CONFIG.API_VERSION
    }/dialog/oauth?${params.toString()}`;

    return {
      success: true,
      url: oauthUrl,
    };
  } catch (error) {
    return {
      success: false,
      error: "Failed to build OAuth URL",
      details: [error.message],
    };
  }
};

/**
 * Validates Facebook OAuth response parameters
 * @param {object} params - OAuth response parameters
 * @returns {object} Validation result
 */
export const validateOAuthResponse = (params) => {
  const errors = [];

  if (!params) {
    errors.push("OAuth response parameters are required");
    return { isValid: false, errors };
  }

  // Check for error in response
  if (params.error) {
    errors.push(`OAuth error: ${params.error}`);
    if (params.error_description) {
      errors.push(`Error description: ${params.error_description}`);
    }
  }

  // Validate required success parameters
  if (!params.error && !params.code) {
    errors.push("Authorization code is missing from OAuth response");
  }

  if (!params.state) {
    errors.push("State parameter is missing from OAuth response");
  }

  return {
    isValid: errors.length === 0,
    errors,
    hasError: !!params.error,
  };
};

/**
 * Validates account data structure
 * @param {object} account - Account object to validate
 * @returns {object} Validation result
 */
export const validateAccountData = (account) => {
  const errors = [];

  if (!account) {
    errors.push("Account data is required");
    return { isValid: false, errors };
  }

  if (!account.id) {
    errors.push("Account ID is required");
  }

  if (!account.accountName) {
    errors.push("Account name is required");
  }

  if (!account.accountId) {
    errors.push("Account ID is required");
  }

  if (account.type && !["chat", "page"].includes(account.type)) {
    errors.push("Account type must be 'chat' or 'page'");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Safe error handler for Facebook Auth operations
 * @param {Error} error - Error object
 * @param {string} operation - Operation that failed
 * @returns {object} Standardized error response
 */
export const handleFacebookAuthError = (error, operation = "Facebook Auth") => {
  console.error(`${operation} Error:`, error);

  // Don't expose sensitive error details to the user
  const userFriendlyErrors = {
    "Network Error":
      "Unable to connect to Facebook. Please check your internet connection.",
    Timeout: "The request took too long. Please try again.",
    Unauthorized: "Authentication failed. Please try logging in again.",
    Forbidden: "You don't have permission to access this resource.",
    "Not Found": "The requested resource was not found.",
    "Internal Server Error": "A server error occurred. Please try again later.",
  };

  const errorMessage = error.message || "An unexpected error occurred";
  const userMessage =
    userFriendlyErrors[errorMessage] ||
    "An error occurred during Facebook authentication. Please try again.";

  return {
    success: false,
    error: userMessage,
    technical:
      process.env.NODE_ENV === "development" ? errorMessage : undefined,
  };
};
