import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Row,
  Col,
  CardTitle,
  CardText,
  Label,
  Button,
  Form,
  Input,
  Alert,
  Spinner,
} from "reactstrap";
import { X, Check, AlertCircle } from "react-feather";
import { toast } from "react-hot-toast";

import { useSkin } from "@hooks/useSkin";
import InputPasswordToggle from "@components/input-password-toggle";
import illustrationsLight from "@src/assets/images/pages/register-v2.svg";
import illustrationsDark from "@src/assets/images/pages/register-v2-dark.svg";
import { signup } from "../../../services/auth";

import "@styles/react/pages/page-authentication.scss";
import Logo from "../../../assets/logo/LOGO.png";
import "../misc/styles/index.scss";
import "./styles/password-requirements.scss";

// Componente moderno para mostrar validaciones de contraseña
const PasswordRequirement = ({ isValid, text }) => {
  return (
    <div className="password-requirement-item">
      <div
        className={`password-requirement-icon ${isValid ? "valid" : "invalid"}`}
      >
        {isValid ? <Check size={12} /> : <AlertCircle size={12} />}
      </div>
      <span
        className={`password-requirement-text ${isValid ? "valid" : "invalid"}`}
      >
        {text}
      </span>
    </div>
  );
};

const Register = () => {
  const { t } = useTranslation();
  const { skin } = useSkin();
  const navigate = useNavigate();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [fullname, setFullname] = useState("");
  const [terms, setTerms] = useState(false);
  const [showDuplicateEmailError, setShowDuplicateEmailError] = useState(false);
  const [showGenericError, setShowGenericError] = useState(false);
  const [loading, setLoading] = useState(false);
  const [passwordRequirements, setPasswordRequirements] = useState({
    hasMinLength: false,
    hasUpperCase: false,
    hasLowerCase: false,
    hasNumber: false,
    hasSpecialChar: false,
  });

  const source = skin === "dark" ? illustrationsDark : illustrationsLight;

  const validatePassword = (password) => {
    setPasswordRequirements({
      hasMinLength: password.length >= 8,
      hasUpperCase: /[A-Z]/.test(password),
      hasLowerCase: /[a-z]/.test(password),
      hasNumber: /[0-9]/.test(password),
      hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    });
  };

  const isPasswordValid = () => {
    return Object.values(passwordRequirements).every(
      (requirement) => requirement
    );
  };

  const onSubmit = async (e) => {
    e.preventDefault();

    if (!terms) {
      return toast.error(t("register.toastTermsError"));
    }

    if (!isPasswordValid()) {
      return toast.error(t("register.passwordRequirementsError"));
    }

    setLoading(true);
    signup(email, password, fullname)
      .then(() => {
        navigate("/verify-email", { state: { email } });
      })
      .catch((err) => {
        if (err.response.data.message === "user already exist") {
          setShowDuplicateEmailError(true);
        } else {
          setShowGenericError(true);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <div className="auth-wrapper auth-cover">
      <Row className="auth-inner m-0">
        <Link className="brand-logo" to="/" onClick={(e) => e.preventDefault()}>
          <div className="logoContainer">
            <img src={Logo} alt="logo" />
          </div>
        </Link>
        <Col className="d-none d-lg-flex align-items-center p-5" lg="8" sm="12">
          <div className="w-100 d-lg-flex align-items-center justify-content-center px-5">
            <img className="img-fluid" src={source} alt="Login Cover" />
          </div>
        </Col>
        <Col
          className="d-flex align-items-center auth-bg px-2 p-lg-5"
          lg="4"
          sm="12"
        >
          <Col className="px-xl-2 mx-auto" sm="8" md="6" lg="12">
            <CardTitle tag="h2" className="fw-bold mb-1">
              {t("register.registerTitle")}
            </CardTitle>
            <CardText className="mb-2">
              {t("register.registerSubtitle")} <b>BiitBot</b>
            </CardText>
            <Alert color="danger" isOpen={showGenericError}>
              <div className="alert-body">
                <X
                  size={15}
                  className="cursor-pointer d-block ms-auto"
                  onClick={() => setShowGenericError(false)}
                />{" "}
                <p className="mb-0">{t("register.genericErrorMessage")}</p>
              </div>
            </Alert>

            <Alert color="danger" isOpen={showDuplicateEmailError}>
              <div className="alert-body">
                <X
                  size={15}
                  className="cursor-pointer d-block ms-auto"
                  onClick={() => setShowDuplicateEmailError(false)}
                />{" "}
                <p className="mb-0">
                  {t("register.duplicateEmailErrorMessage")}
                </p>
              </div>
            </Alert>
            <Form
              action="/"
              className="auth-register-form mt-2"
              onSubmit={onSubmit}
            >
              <div className="mb-1">
                <Label className="form-label" for="register-name">
                  {t("register.fullnameLabel")}
                </Label>
                <Input
                  value={fullname}
                  onChange={(e) => setFullname(e.target.value)}
                  autoFocus
                  required
                  placeholder={t("register.fullnameLabel")}
                  id="register-name"
                />
              </div>
              <div className="mb-1">
                <Label className="form-label" for="email">
                  {t("register.emailLabel")}
                </Label>
                <Input
                  type="email"
                  required
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              <div className="mb-1">
                <Label className="form-label" for="password">
                  {t("register.passwordLabel")}
                </Label>
                <InputPasswordToggle
                  className="input-group-merge"
                  id="password"
                  required
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value);
                    validatePassword(e.target.value);
                  }}
                />
                {password && (
                  <div className="password-requirements-container">
                    <span className="password-requirements-title">
                      {t("register.passwordRequirementsTitle")}
                    </span>
                    <div className="row">
                      <div className="col-12 col-md-6">
                        <PasswordRequirement
                          isValid={passwordRequirements.hasMinLength}
                          text={t("register.passwordMinLength")}
                        />
                        <PasswordRequirement
                          isValid={passwordRequirements.hasUpperCase}
                          text={t("register.passwordUpperCase")}
                        />
                        <PasswordRequirement
                          isValid={passwordRequirements.hasLowerCase}
                          text={t("register.passwordLowerCase")}
                        />
                      </div>
                      <div className="col-12 col-md-6">
                        <PasswordRequirement
                          isValid={passwordRequirements.hasNumber}
                          text={t("register.passwordNumber")}
                        />
                        <PasswordRequirement
                          isValid={passwordRequirements.hasSpecialChar}
                          text={t("register.passwordSpecialChar")}
                        />
                      </div>
                    </div>
                    {isPasswordValid() && (
                      <div className="password-valid-banner">
                        <div className="password-valid-content">
                          <Check size={16} className="password-valid-icon" />
                          <small className="password-valid-text">
                            {t("register.passwordValidMessage")}
                          </small>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
              <div className="form-check mb-1">
                <Input
                  id="terms"
                  type="checkbox"
                  checked={terms}
                  onChange={(e) => setTerms(e.target.checked)}
                />
                <Label className="form-check-label" for="terms">
                  {t("register.acceptTermsLabel")}{" "}
                  <a
                    className="ms-25"
                    href="https://biitbot.com/terms-and-conditions"
                    target="_blank"
                    rel="noreferrer"
                  >
                    {t("register.termsLink")}
                  </a>{" "}
                  |{" "}
                  <a
                    className="ms-25"
                    href="https://biitbot.com/privacy-policy"
                    target="_blank"
                    rel="noreferrer"
                  >
                    {t("register.privacyPolicyLink")}
                  </a>
                </Label>
              </div>
              <Button type="submit" block color="primary" disabled={loading}>
                {loading ? <Spinner /> : t("register.registerButton")}
              </Button>
            </Form>
            <p className="text-center mt-2">
              <span className="me-25">{t("register.loginLinkText")}</span>
              <Link to="/login">
                <span>{t("register.loginLink")}</span>
              </Link>
            </p>
          </Col>
        </Col>
      </Row>
    </div>
  );
};

export default Register;
