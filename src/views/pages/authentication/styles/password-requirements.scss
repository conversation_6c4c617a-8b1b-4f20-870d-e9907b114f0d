// Estilos modernos para los requisitos de contraseña
.password-requirements-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 1.25rem;
  margin-top: 0.75rem;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }
}

.password-requirements-title {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #6c757d;
  margin-bottom: 0.75rem;
  display: block;
}

.password-requirement-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  transition: all 0.2s ease;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.password-requirement-icon {
  width: 20px;
  height: 20px;
  min-width: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  transition: all 0.3s ease;
  
  &.valid {
    background-color: #d1e7dd;
    color: #0f5132;
    transform: scale(1.05);
  }
  
  &.invalid {
    background-color: #f8f9fa;
    color: #6c757d;
  }
}

.password-requirement-text {
  font-size: 0.8rem;
  transition: all 0.2s ease;
  
  &.valid {
    color: #198754;
    font-weight: 500;
  }
  
  &.invalid {
    color: #6c757d;
  }
}

.password-valid-banner {
  background: linear-gradient(135deg, #d1e7dd 0%, #a3d9a4 100%);
  border: 1px solid #badbcc;
  border-radius: 8px;
  padding: 0.75rem;
  margin-top: 0.75rem;
  animation: slideIn 0.3s ease;
  
  .password-valid-content {
    display: flex;
    align-items: center;
    
    .password-valid-icon {
      color: #0f5132;
      margin-right: 0.5rem;
    }
    
    .password-valid-text {
      color: #0f5132;
      font-weight: 500;
      font-size: 0.85rem;
      margin: 0;
    }
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .password-requirements-container {
    padding: 1rem;
  }
  
  .password-requirement-item {
    margin-bottom: 0.75rem;
  }
  
  .password-requirement-text {
    font-size: 0.75rem;
  }
}

// Dark mode support
[data-bs-theme="dark"] {
  .password-requirements-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-color: #495057;
  }
  
  .password-requirements-title {
    color: #adb5bd;
  }
  
  .password-requirement-icon {
    &.invalid {
      background-color: #495057;
      color: #adb5bd;
    }
  }
  
  .password-requirement-text {
    &.invalid {
      color: #adb5bd;
    }
  }
  
  .password-valid-banner {
    background: linear-gradient(135deg, #155724 0%, #1e7e34 100%);
    border-color: #28a745;
  }
}
