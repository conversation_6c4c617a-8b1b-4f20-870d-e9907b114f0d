// ** React Imports
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import toast from "react-hot-toast";
import { useForm, Controller } from "react-hook-form";
import { AlertCircle, X } from "react-feather";
import {
  Row,
  Col,
  Form,
  Input,
  Label,
  Button,
  CardText,
  CardTitle,
  FormFeedback,
  Alert,
} from "reactstrap";
import { useContext, useState } from "react";

import { useSkin } from "@hooks/useSkin";
import Avatar from "@components/avatar";
import InputPasswordToggle from "@components/input-password-toggle";
import illustrationsLight from "@src/assets/images/pages/login-v2.svg";
import illustrationsDark from "@src/assets/images/pages/login-v2-dark.svg";
import { AbilityContext } from "@src/utility/context/Can";

import { authRequest } from "../../../services";
import "@styles/react/pages/page-authentication.scss";
import Logo from "../../../assets/logo/LOGO.png";
import "../misc/styles/index.scss";
import { setCookie, setUserRoles } from "../../../utility/Utils";

const ToastContentEmailNotVerified = ({ toastId, translate }) => {
  return (
    <div className="d-flex">
      <div className="me-1">
        <Avatar size="sm" color="danger" icon={<AlertCircle size={12} />} />
      </div>
      <div className="d-flex flex-column">
        <div className="d-flex justify-content-between">
          <X
            size={12}
            className="cursor-pointer"
            onClick={() => toast.dismiss(toastId)}
          />
        </div>
        <span>{translate("login.emailNotVerifiedToast.message")}</span>
      </div>
    </div>
  );
};

const AlertResendVerificationEmail = ({ onResend, t }) => {
  return (
    <Alert color="warning">
      <div className="alert-body d-flex">
        <span className="ms-1">
          {t("login.resendVerificationEmailAlert.message")}{" "}
          <span className="text-primary cursor-pointer" onClick={onResend}>
            {t("login.resendVerificationEmailAlert.resendLink")}
          </span>
        </span>
      </div>
    </Alert>
  );
};

const ToastContentGenericError = ({ toastId, translate }) => {
  return (
    <div className="d-flex">
      <div className="me-1">
        <Avatar size="sm" color="danger" icon={<AlertCircle size={12} />} />
      </div>
      <div className="d-flex flex-column">
        <div className="d-flex justify-content-between">
          <X
            size={12}
            className="cursor-pointer"
            onClick={() => toast.dismiss(toastId)}
          />
        </div>
        <span>{translate("login.genericErrorToast.message")}</span>
      </div>
    </div>
  );
};

const defaultValues = {
  password: "",
  loginEmail: "",
};

const Login = () => {
  const { t } = useTranslation();
  const ability = useContext(AbilityContext);
  const { skin } = useSkin();
  const {
    control,
    setError,
    handleSubmit,
    getValues,
    formState: { errors },
  } = useForm({ defaultValues });

  const [showResendVerificationEmail, setShowResendVerificationEmail] =
    useState(false);

  const source = skin === "dark" ? illustrationsDark : illustrationsLight;

  const [rememberMe, setRememberMe] = useState(false);

  const onSubmit = (data) => {
    if (Object.values(data).every((field) => field.length > 0)) {
      authRequest
        .login(data.loginEmail, data.password)
        .then((res) => {
          const userData = {
            name: res.userData.name,
            email: res.userData.email,
            ability: res.userData.ability,
            subscription: res.userData.company.subscription,
            rememberMe: rememberMe ? "true" : "false",
            userId: res.userData.id,
          };

          // Guardar tokens y datos del usuario
          if (rememberMe) {
            setCookie("userData", JSON.stringify(userData), 10);
            setCookie("accessToken", res.accessToken, 10);
            // Guardar refreshToken si está disponible
            if (res.refreshToken) {
              setCookie("refreshToken", res.refreshToken, 10);
            }
          } else {
            sessionStorage.setItem("userData", JSON.stringify(userData));
            sessionStorage.setItem("accessToken", res.accessToken);
            // Guardar refreshToken si está disponible
            if (res.refreshToken) {
              sessionStorage.setItem("refreshToken", res.refreshToken);
            }
          }

          setUserRoles(res.userData.role);
          ability.update(res.userData.ability);

          // Verificar si necesita actualizar contraseña
          if (res.shouldUpdatePassword) {
            window.location.href = "/change-password";
          } else {
            window.location.href = "/hub/chat";
          }
        })
        .catch((err) => {
          console.error("Error en login:", err.message);
          // Manejar errores específicos del backend
          const errorMessage = err.message;

          // Verificar si es error de email no confirmado
          if (errorMessage.includes("confirme su email")) {
            setShowResendVerificationEmail(true);
            return toast((toastObj) => (
              <ToastContentEmailNotVerified
                toastId={toastObj.id}
                translate={t}
              />
            ));
          }

          // Verificar si es error de credenciales inválidas
          if (errorMessage.includes("incorrectos")) {
            return setError("loginEmail", {
              type: "manual",
              message: errorMessage,
            });
          }

          // Verificar si es error de contraseña muy corta
          if (errorMessage.includes("6 caracteres")) {
            return setError("password", {
              type: "manual",
              message: errorMessage,
            });
          }

          // Verificar si es error de email inválido
          if (errorMessage.includes("email válido")) {
            return setError("loginEmail", {
              type: "manual",
              message: errorMessage,
            });
          }

          // Verificar si es error de campos requeridos
          if (errorMessage.includes("complete todos los campos")) {
            return setError("loginEmail", {
              type: "manual",
              message: errorMessage,
            });
          }

          // Para otros errores, mostrar toast genérico
          return toast((toastObj) => (
            <ToastContentGenericError toastId={toastObj.id} translate={t} />
          ));
        });
    } else {
      for (const key in data) {
        if (data[key].length === 0) {
          setError(key, {
            type: "manual",
          });
        }
      }
    }
  };

  const handleResendVerificationEmail = () => {
    const email = getValues("loginEmail");
    authRequest.resendVerificationEmail(email).then(() => {
      toast.success(t("login.resendVerificationEmailAlert.successMessage"));
    });
  };

  return (
    <div className="auth-wrapper auth-cover">
      <Row className="auth-inner m-0">
        <Link className="brand-logo" to="/" onClick={(e) => e.preventDefault()}>
          <div className="logoContainer">
            <img src={Logo} alt="logo" />
          </div>
        </Link>
        <Col className="d-none d-lg-flex align-items-center p-5" lg="8" sm="12">
          <div className="w-100 d-lg-flex align-items-center justify-content-center px-5">
            <img className="img-fluid" src={source} alt="Login Cover" />
          </div>
        </Col>
        <Col
          className="d-flex align-items-center auth-bg px-2 p-lg-5"
          lg="4"
          sm="12"
        >
          <Col className="px-xl-2 mx-auto" sm="8" md="6" lg="12">
            <CardTitle tag="h2" className="fw-bold mb-1">
              {t("login.loginTitle")}
            </CardTitle>
            <CardText className="mb-2">
              {t("login.loginSubtitle")} <b>Biitbot</b>
            </CardText>
            {showResendVerificationEmail && (
              <AlertResendVerificationEmail
                onResend={handleResendVerificationEmail}
                t={t}
              />
            )}
            <Form
              className="auth-login-form mt-2"
              onSubmit={handleSubmit(onSubmit)}
            >
              <div className="mb-1">
                <Label className="form-label" for="login-email">
                  {t("login.emailLabel")}
                </Label>
                <Controller
                  id="loginEmail"
                  name="loginEmail"
                  control={control}
                  render={({ field }) => (
                    <Input
                      autoFocus
                      type="email"
                      placeholder="<EMAIL>"
                      invalid={errors.loginEmail && true}
                      {...field}
                    />
                  )}
                />
                {errors.loginEmail && (
                  <FormFeedback>{errors.loginEmail.message}</FormFeedback>
                )}
              </div>
              <div className="mb-1">
                <div className="d-flex justify-content-between">
                  <Label className="form-label" for="login-password">
                    {t("login.passwordLabel")}
                  </Label>
                  <Link to="/forgot-password">
                    <small>{t("login.forgotPasswordLink")}</small>
                  </Link>
                </div>
                <Controller
                  id="password"
                  name="password"
                  control={control}
                  render={({ field }) => (
                    <InputPasswordToggle
                      className="input-group-merge"
                      invalid={errors.password && true}
                      {...field}
                    />
                  )}
                />
              </div>
              <div className="form-check mb-1">
                <Input
                  type="checkbox"
                  id="remember-me"
                  onChange={() => setRememberMe(!rememberMe)}
                  checked={rememberMe}
                />
                <Label className="form-check-label" for="remember-me">
                  {t("login.rememberMeLabel")}
                </Label>
              </div>
              <Button type="submit" color="primary" block>
                {t("login.loginButton")}
              </Button>
            </Form>
            <p className="text-center mt-2">
              <span className="me-25">{t("login.newToBiitbotText")}</span>
              <Link to="/register">
                <span>{t("login.createAccountLink")}</span>
              </Link>
            </p>
          </Col>
        </Col>
      </Row>
    </div>
  );
};

export default Login;
