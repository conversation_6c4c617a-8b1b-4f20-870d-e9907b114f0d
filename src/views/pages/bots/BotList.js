import { Fragment, useEffect, useState } from "react";
import {
  Card,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  UncontrolledButtonDropdown,
} from "reactstrap";
import DataTable from "react-data-table-component";
import Swal from "sweetalert2";
import withReactContent from "sweetalert2-react-content";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";

import SidebarCreateBot from "./SidebarCreateBot";
import { columns } from "./columns";
import { createBot, deleteBot } from "../../../services/bot";
import SidebarUpdateBot from "./SidebarUpdateBot";
import DocumentationChatbot from "../bot/DocumentationChatbot";
import EmptyStateCreate from "../../../@core/components/empty-state/empty-state-create";
import WhatsappQr from "../../../@core/components/whatsapp-qr";
import FacebookAuth from "../../../@core/components/integratoins/facebook";
import whatsappLogo from "../../../assets/images/icons/whatsapp.png";
import webIcon from "../../../assets/images/icons/internet.png";
import instagramIcon from "../../../assets/images/icons/social/instagram.png";
import slackIcon from "../../../assets/images/icons/social/slack.png";
import discordIcon from "../../../assets/images/icons/discord.png";
import telegramIcon from "../../../assets/images/icons/telegram.png";
import facebookMessagerIcion from "../../../assets/images/icons/facebook-messenger.png";
import SidebarCompleteCalendarIntegration from "./SidebarCompleteCalendarIntegration";
import { completeIntegration } from "../../../services/calendar";

import "@styles/react/libs/react-select/_react-select.scss";
import "@styles/react/libs/tables/react-dataTable-component.scss";

// Componente reutilizable para los dropdowns de providers
const ProviderDropdownItems = ({ handleBotCreateButton, t }) => {
  return (
    <>
      <DropdownItem
        tag="button"
        className="d-flex align-items-center gap-1"
        onClick={() => handleBotCreateButton("web")}
      >
        <img src={webIcon} alt="web logo" width={20} />
        {t("bot.listOfBots.createBotForWebsite")}
      </DropdownItem>
      <DropdownItem
        tag="button"
        className="d-flex align-items-center gap-1"
        onClick={() => handleBotCreateButton("whatsapp")}
      >
        <img src={whatsappLogo} alt="whatsapp logo" width={20} />
        {t("bot.listOfBots.createBotForWhatsapp")}
      </DropdownItem>
      <div className="divider">
        <div className="divider-text">
          {t("bot.listOfBots.comingSoonProviders")}
        </div>
      </div>
      <div>
        <DropdownItem
          tag="button"
          disabled
          className="d-flex align-items-center gap-1"
        >
          <img
            src={telegramIcon}
            alt="telegram logo"
            width={20}
            style={{ opacity: 0.5, filter: "grayscale(100%)" }}
          />
          {t("bot.listOfBots.providers.telegram")}
        </DropdownItem>
        <DropdownItem
          tag="button"
          disabled
          className="d-flex align-items-center gap-1"
        >
          <img
            src={instagramIcon}
            alt="instagram logo"
            width={20}
            style={{ opacity: 0.5, filter: "grayscale(100%)" }}
          />
          {t("bot.listOfBots.providers.instagram")}
        </DropdownItem>
        <DropdownItem
          tag="button"
          disabled
          className="d-flex align-items-center gap-1"
        >
          <img
            src={facebookMessagerIcion}
            alt="facebook messenger logo"
            width={20}
            style={{ opacity: 0.5, filter: "grayscale(100%)" }}
          />
          {t("bot.listOfBots.providers.facebookMessenger")}
        </DropdownItem>
        <DropdownItem
          className="d-flex align-items-center gap-1"
          tag="button"
          disabled
        >
          <img
            src={slackIcon}
            alt="slack logo"
            width={20}
            style={{ opacity: 0.5, filter: "grayscale(100%)" }}
          />
          {t("bot.listOfBots.providers.slack")}
        </DropdownItem>
        <DropdownItem
          tag="button"
          disabled
          className="d-flex align-items-center gap-1"
        >
          <img
            src={discordIcon}
            alt="discord logo"
            width={20}
            style={{ opacity: 0.5, filter: "grayscale(100%)" }}
          />
          {t("bot.listOfBots.providers.discord")}
        </DropdownItem>
      </div>
    </>
  );
};

const BotList = ({ data, updateBotList }) => {
  const { t } = useTranslation();
  const location = useLocation();
  const params = new URLSearchParams(location.search);

  const [sidebarCreateBotOpen, setSidebarCreateBotOpen] = useState(false);
  const [sidebarUpdateBotOpen, setSidebarUpdateBotOpen] = useState(false);
  const [selectedBot, setSelectedBot] = useState(null);
  const [loading, setLoading] = useState(false);
  const [providerSelected, setProviderSelected] = useState(null);
  const [showIntegrationModal, setShowIntegrationModal] = useState(false);
  const [showCalendarIntegrationForm, setShowCalendarIntegrationForm] =
    useState(false);
  const [submittingIntegration, setSubmittingIntagration] = useState(false);

  const NotificationSwal = withReactContent(Swal);

  const toggleSidebar = () => {
    setSidebarCreateBotOpen(!sidebarCreateBotOpen);
  };

  const handleOnDelete = async (id) => {
    return NotificationSwal.fire({
      title: t("bot.listOfBots.delete.confirmation.deleteBotTitle"),
      text: t("bot.listOfBots.delete.confirmation.deleteBotDescription"),
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: t("general.yesDelete"),
      customClass: {
        confirmButton: "btn btn-primary",
        cancelButton: "btn btn-outline-danger ms-1",
      },
      buttonsStyling: false,
    }).then(async (result) => {
      if (result.value) {
        await deleteBot(id)
          .then(() => {
            updateBotList();
            return NotificationSwal.fire({
              icon: "success",
              title: t("bot.listOfBots.delete.success.deleteBotTitle"),
              text: t("bot.listOfBots.delete.success.deleteBotDescription"),
              customClass: {
                confirmButton: "btn btn-success",
              },
            });
          })
          .catch(() => {
            return NotificationSwal.fire({
              icon: "error",
              title: t("bot.listOfBots.error.deleteBotTitle"),
              text: t("bot.listOfBots.error.deleteBotDescription"),
              customClass: {
                confirmButton: "btn btn-success",
              },
            });
          });
      }
    });
  };

  const handleOnCreate = async (data) => {
    setLoading(true);
    data.providerConfig = {
      provider: providerSelected,
    };
    try {
      await createBot(data);
      setSidebarCreateBotOpen(!sidebarCreateBotOpen);
      updateBotList();
      setLoading(false);
    } catch (error) {
      NotificationSwal.fire({
        icon: "error",
        title: t("bot.listOfBots.error.createBotTitle"),
        text: t("bot.listOfBots.error.createBotDescription"),
        customClass: {
          confirmButton: "btn btn-success",
        },
      });
    }
  };

  const handleOnEdit = async (botId) => {
    setSelectedBot(data.find((bot) => bot.id === botId));
    setSidebarUpdateBotOpen(!sidebarUpdateBotOpen);
  };

  const handleBotCreateButton = (provider = "web") => {
    setProviderSelected(provider);
    toggleSidebar();
  };

  const handleShowIntegration = (bot) => {
    setSelectedBot(bot);
    setShowIntegrationModal(true);
  };

  const handleCalendarComplete = async (data) => {
    setSubmittingIntagration(true);
    const result = await completeIntegration(data);
    if (result.status === "complete") {
      NotificationSwal.fire({
        icon: "success",
        title: "Integracion exitosa",
        text: `La integracion con google calendar con ${data.botRequestId} fue exitosa`,
        customClass: {
          confirmButton: "btn btn-success",
        },
        confirmButtonText: "Finalizar",
      })
        .then(() => {
          setShowCalendarIntegrationForm(false);
          window.history.replaceState({}, "", window.location.pathname);
        })
        .catch(() => {
          NotificationSwal.fire({
            icon: "error",
            title: "Error al completar la integracion",
            customClass: {
              confirmButton: "btn btn-primary",
            },
            confirmButtonText: "Intentarlo nuevamente",
          });
        });
    }

    setSubmittingIntagration(false);
  };

  useEffect(() => {
    const calendarIntegration =
      params.get("calendarIntegration") == "true" ? true : false;

    if (data.length > 0 && calendarIntegration) {
      const integrationBotRequestId = params.get("botRequestId");

      NotificationSwal.fire({
        icon: "success",
        title: "Integracion exitosa",
        text: `La integracion con google calendar con ${integrationBotRequestId} fue exitosa`,
        customClass: {
          confirmButton: "btn btn-success",
        },
        confirmButtonText: "Completar integracion",
      }).then(() => {
        setShowCalendarIntegrationForm(true);
      });
    }

    const instagramIntegration =
      params.get("instagramIntegration") == "true" ? true : false;

    if (data.length > 0 && instagramIntegration) {
      const integrationBotRequestId = params.get("botRequestId");

      NotificationSwal.fire({
        icon: "success",
        title: "Integracion exitosa",
        text: `La integracion con instagram con ${integrationBotRequestId} fue exitosa`,
        customClass: {
          confirmButton: "btn btn-success",
        },
        confirmButtonText: "Completar integracion",
      }).then(() => {
        // remove params from url
        window.history.replaceState({}, "", window.location.pathname);
      });
    }
  }, []);

  return (
    <Fragment>
      <Card>
        <div className="react-dataTable">
          <DataTable
            subHeader
            responsive
            columns={columns(
              handleOnDelete,
              handleOnEdit,
              handleShowIntegration,
              t
            )}
            className="react-dataTable overflow-visible"
            data={data}
            subHeaderComponent={
              <div className="d-flex justify-content-between">
                <UncontrolledButtonDropdown>
                  <DropdownToggle color="primary" caret>
                    {t("bot.listOfBots.createBot")}
                  </DropdownToggle>
                  <DropdownMenu>
                    <ProviderDropdownItems
                      handleBotCreateButton={handleBotCreateButton}
                      t={t}
                    />
                  </DropdownMenu>
                </UncontrolledButtonDropdown>
              </div>
            }
            noDataComponent={
              <EmptyStateCreate
                title={t("bot.listOfBots.empty")}
                message={t("bot.listOfBots.emptyDescription")}
              >
                <div className="d-flex justify-content-between">
                  <UncontrolledButtonDropdown>
                    <DropdownToggle color="primary" caret>
                      {t("bot.listOfBots.createBot")}
                    </DropdownToggle>
                    <DropdownMenu>
                      <ProviderDropdownItems
                        handleBotCreateButton={handleBotCreateButton}
                        t={t}
                      />
                    </DropdownMenu>
                  </UncontrolledButtonDropdown>
                </div>
              </EmptyStateCreate>
            }
          />
        </div>
      </Card>
      <SidebarCreateBot
        open={sidebarCreateBotOpen}
        toggleSidebar={toggleSidebar}
        onCreate={handleOnCreate}
        provider={providerSelected}
      />
      <SidebarUpdateBot
        open={sidebarUpdateBotOpen}
        toggleSidebar={() => {
          setSidebarUpdateBotOpen(!sidebarUpdateBotOpen);
          updateBotList();
        }}
        bot={selectedBot}
      />
      <Modal isOpen={loading} toggle={() => setLoading(!loading)}>
        <ModalBody className="p-4 d-flex justify-content-center align-items-center">
          <div className="d-flex flex-column align-items-center">
            <span className="align-middle mb-2">{t("bot.creatingBot")}</span>
            <Spinner color="primary" />
          </div>
        </ModalBody>
      </Modal>
      <Modal
        isOpen={showIntegrationModal}
        toggle={() => setShowIntegrationModal(!showIntegrationModal)}
        className="modal-dialog-centered"
        size="lg"
      >
        <ModalHeader>
          {t("bot.howToIntegrate", {
            botType: selectedBot?.chatProvider,
          })}
        </ModalHeader>
        <ModalBody className="p-4 d-flex justify-content-center align-items-center">
          {selectedBot?.chatProvider === "web" && <DocumentationChatbot />}
          {selectedBot?.chatProvider === "whatsapp" && (
            <WhatsappQr
              config={selectedBot?.whatsappConfig}
              botRequestId={selectedBot?.botRequestId}
            />
          )}
          {selectedBot?.chatProvider === "instagram" && (
            <FacebookAuth botRequestId={selectedBot.botRequestId} />
          )}
        </ModalBody>
      </Modal>
      {showCalendarIntegrationForm && (
        <SidebarCompleteCalendarIntegration
          isLoading={submittingIntegration}
          open={showCalendarIntegrationForm}
          toggleSidebar={() => {
            setShowCalendarIntegrationForm(true);
          }}
          botRequestId={params.get("botRequestId")}
          onSubmit={handleCalendarComplete}
        />
      )}
    </Fragment>
  );
};

export default BotList;
