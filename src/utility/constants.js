console.log("project is running on prod env:", import.meta.env.PROD);

const isProduction = import.meta.env.PROD;

export const ENV_SETTING = {
  VITE_API_URL: isProduction
    ? "https://api.biitbot.com"
    : "http://localhost:3000",
  VITE_WEBSOCKET_URL: isProduction
    ? "wss://api.biitbot.com"
    : "ws://localhost:3000",
};

// Facebook/Instagram Integration Configuration
export const FACEBOOK_CONFIG = {
  // Facebook Graph API Version - Updated to v23.0 for consistency
  API_VERSION: "v23.0",

  // Client ID from environment variables
  CLIENT_ID: import.meta.env.VITE_FACEBOOK_CLIENT_ID,

  // Dynamic callback URLs based on environment
  CALLBACK_URL: isProduction
    ? "https://app.biitbot.com/social-auth/facebook/callback"
    : "https://caiman-legal-treefrog.ngrok-free.app/social-auth/facebook/callback",

  // OAuth Scopes for Instagram Business API
  SCOPES: [
    "instagram_business_basic",
    "instagram_business_manage_messages",
    "pages_show_list",
    "instagram_manage_messages",
    "instagram_basic",
    "human_agent",
  ],

  // OAuth Configuration
  OAUTH_CONFIG: {
    display: "page",
    response_type: "code",
    extras: JSON.stringify({ setup: { channel: "IG_API_ONBOARDING" } }),
  },

  // Base URLs
  OAUTH_BASE_URL: "https://www.facebook.com",
  GRAPH_API_BASE_URL: "https://graph.facebook.com",
};

// Validation helpers
export const validateFacebookConfig = () => {
  const errors = [];

  if (!FACEBOOK_CONFIG.CLIENT_ID) {
    errors.push("VITE_FACEBOOK_CLIENT_ID environment variable is required");
  }

  if (!FACEBOOK_CONFIG.CALLBACK_URL) {
    errors.push("Facebook callback URL is not configured");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const PERMISSIONS_LIST = {
  CREATE_BOT: "create_bot",
  UPDATE_BOT: "update_bot",
  DELETE_BOT: "delete_bot",
  READ_BOT: "read_bot",
  READ_CHAT: "read_chat",
  DELETE_CHAT: "delete_chat",
  REPLY_CHAT: "reply_chat",
  READ_COMPANY: "read_company",
  UPDATE_COMPANY: "update_company",
  DELETE_COMPANY: "delete_company",
  READ_USER: "read_user",
  UPDATE_USER: "update_user",
  CREATE_USER: "create_user",
  DELETE_USER: "delete_user",
  ASSIGN_ROLE_USER: "assign_role_user",
  DELETE_ROLE_USER: "delete_role_user",
  READ_PERMISSION: "read_permission",
  ASSIGN_PERMISSION: "assign_permission",
  REMOVE_PERMISSION: "remove_permission",
  READ_ROLE: "read_role",
  UPDATE_ROLE: "update_role",
  DELETE_ROLE: "delete_role",
  CREATE_ROLE: "create_role",
  READ_STATISTICS: "read_statistics",
  CAN_READ_PLAN: "can_read_plan",
  READ_CONTACT: "read_contact",
  CREATE_CONTACT: "create_contact",
  DELATE_CONTACT: "delate_contact",
  DISABLE_AI: "disable_ai",
  READ_DATA_SOURCE: "read_data_source",
  DELETE_DATA_SOURCE: "delete_data_source",
  UPDATE_DATA_SOURCE: "update_data_source",
  CREATE_DATA_SOURCE: "create_data_source",
  READ_TEAM: "read_team",
  UPDATE_TEAM: "update_team",
  DELETE_TEAM: "delete_team",
  ADD_USER_TEAM: "add_user_team",
  CREATE_TEAM: "create_team",
};
