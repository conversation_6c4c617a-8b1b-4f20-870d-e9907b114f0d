{"general": {"seconds": "seconds", "messages": "messages", "delete": "Delete", "conversations": "Conversations", "active": "Active", "actions": "Actions", "createdAt": "Created at", "continue": "Continue", "cancel": "Cancel", "close": "Close", "update": "Update", "edit": "Edit", "save": "Save", "create": "Create", "yes": "Yes", "no": "No", "yesDelete": "Yes, delete!", "ok": "OK", "file": "File", "text": "Text", "id": "ID", "name": "Name", "email": "Email", "dataNotFound": "Data not found", "password": "Password", "fullName": "Full name", "submit": "Submit", "discard": "Discard", "add": "Add", "infoUpdated": "Information updated", "account": "Account", "logOff": "Log off", "loggingOut": "Logging out...", "confirmed": "Confirmed", "notConfirmed": "Not confirmed yet", "characters": "characters", "contactNumber": "Contact number", "isWhatsapp": "Is a WhatsApp number?", "enable": "Enable", "disable": "Disable", "enableAI": "Enable AI", "disableAI": "Disable AI", "AIStatus": "AI Status", "AiEnabled": "AI enabled", "AiDisabled": "AI disabled", "saving": "Saving...", "loading": "Loading...", "processing": "Processing...", "confirm": "Confirm", "confirmAction": "Are you sure you want to perform this action?", "actionCannotBeUndone": "This action cannot be undone.", "saveChanges": "Save Changes"}, "errors": {"suggestions": "Suggestions", "technicalDetails": "Technical Details", "retry": "Retry", "openSettings": "Open Settings", "network": {"title": "Network Connection Error", "message": "Unable to connect to the server. Please check your internet connection.", "checkConnection": "Check your internet connection", "tryAgain": "Try again in a few moments"}, "auth": {"title": "Authentication Error", "message": "There was a problem with your Facebook/Instagram authentication.", "reauthorize": "Re-authorize your Facebook account", "checkPermissions": "Check that all required permissions are granted"}, "config": {"title": "Configuration Error", "message": "There's an issue with the Facebook/Instagram configuration.", "checkSettings": "Check your integration settings", "contactSupport": "Contact support if the problem persists"}, "timeout": {"title": "Request Timeout", "message": "The request took too long to complete.", "tryAgain": "Try the operation again", "checkStatus": "Check Facebook API status"}, "rateLimit": {"title": "Rate Limit Exceeded", "message": "Too many requests have been made. Please wait before trying again.", "waitAndRetry": "Wait a few minutes and try again", "reduceRequests": "Reduce the frequency of requests"}, "general": {"title": "An Error Occurred", "message": "Something went wrong. Please try again.", "tryAgain": "Try the operation again", "contactSupport": "Contact support if the problem continues"}}, "contact": {"createContact": "Create contact", "newContact": "New contact", "contactCreated": "Contact created", "contactNotCreated": "Contact not created", "contactUpdated": "Contact updated", "deleteConfirmationTitle": "Do you want to delete this contact?", "deleteContactSuccess": "Contact deleted successfully", "toggleAiSuccess": "AI enabled successfully", "toggleAiError": "AI could not be enabled", "saveContact": "Save contact"}, "documentation": {"title": "Biitbot - Documentation", "description": "How to integrate biitbot with zapier (instagram, meta, google calendar and outlook calendar)", "button": "See documentation"}, "dashboard": {"selectBot": "Select a bot", "needToSelectBot": "You need to select a bot to view its statistics", "noBots": "You don't have any bots. Create one to view its statistics.", "activeUsers": {"activeConversation": "Active Conversations"}, "feeling": {"feelingTitle": "After analyzing conversations, it is determined that users feel", "feelingTitleComplement": "when interacting with the bot.", "feelingDescription": "This is the sentiment that manifests most frequently."}, "status": {"title": "Average statistics", "averageResponseTime": "Average Response Time", "averageNumberOfMessagesPerUser": "Average Number of Messages per User", "averageNumberOfMessagesGeneratedByTheBot": "Average Number of Messages Generated by the Bot", "responsesGenerated": "responses generated"}, "interactions": {"title": "Number of Interactions"}, "interactionPeak": {"title": "Peak Day and Time of Interactions with the Chatbot"}}, "verifyEmail": {"title": "Verify your email ✉️", "description": "We have sent an email to", "button": "Go to login"}, "hub": {"error": {"couldNotAssignedToTheTeam": "<PERSON><PERSON> could not be assigned to the team"}, "connection": {"connecting": "Connecting...", "connected": "Connected", "disconnected": "Disconnected", "reconnect": "Reconnect"}, "chatAssigned": "<PERSON><PERSON> assigned to the team successfully", "inbox": {"title": "Conversations", "inbox": "Inbox", "unanswered": "Unanswered"}, "chat": {"chatAssignedTo": "<PERSON><PERSON> assigned to", "chatAssigTo": "Assign chat to", "assignChat": "Assign chat", "AiActived": "AI activated", "AiDisabled": "AI disabled", "aiEnabled": "AI Enabled", "aiDisabled": "AI Disabled", "writeMessageHere": "Write your message here", "typeMessage": "Type your message here", "send": "Send", "chatDeleted": "<PERSON><PERSON> deleted successfully", "chatDeletedError": "Chat could not be deleted", "searchChatByName": "Search chat by name"}}, "bot": {"creatingBot": "Creating Bot...", "howToIntegrate": "How to integrate your bot of type {{botType}} ", "listOfBots": {"botName": "Bot Name", "publicKey": "Public Key", "botType": "Bot Type", "integration": "Integration", "seeIntegration": "See Integration", "editBot": "<PERSON>", "deleteBot": "Delete Bot", "createBot": "Create <PERSON><PERSON>", "createBotForWebsite": "Create <PERSON><PERSON> for Website", "createBotForWhatsapp": "Create <PERSON><PERSON> for WhatsApp", "comingSoonProviders": "Coming soon chat providers", "providers": {"telegram": "Telegram", "instagram": "Instagram", "facebookMessenger": "Facebook Messenger", "slack": "<PERSON><PERSON>ck", "discord": "Discord"}, "botDatasources": "Bot - Data Sources", "botIdentity": "Bot - Identity", "botColor": "Bot - Color", "selectYourDataSource": "Select the data source you want to use", "bringYourBotLife": "Bring your bot to life", "configureBotColor": "Configure your bot's colors", "empty": "It seems you haven't created your first Bo<PERSON> yet", "emptyDescription": "You can create a bot using the button below", "creation": {"error": {"createBotTitle": "Error creating bot", "createBotDescription": "The bot could not be created, please try again."}, "success": {"createBotTitle": "<PERSON><PERSON> created successfully", "createBotDescription": "The bot has been created successfully."}}, "delete": {"error": {"deleteBotTitle": "Error deleting bot", "deleteBotDescription": "The bot could not be deleted, please try again."}, "success": {"deleteBotTitle": "<PERSON><PERSON> deleted successfully", "deleteBotDescription": "The bot has been deleted successfully."}, "confirmation": {"deleteBotTitle": "Are you sure you want to delete this bot?", "deleteBotDescription": "This action cannot be reverted!"}}}, "documentation": {"title": "To use the chatbot, add the following line to your index.html file:", "publicKey": "Public Key", "clickToCopy": "Click to copy", "note": "* Make sure the chatbot script is available at the specified path and replace YOUR_PUBLIC_KEY with your public key:"}}, "dataSources": {"dataSourceInUse": "Data source in use", "dataSourceContent": "Data source content", "changeDataSource": "Change data source", "dataSourceUpdatedTitle": "Data source updated", "dataSourceUpdatedMessage": "Your data source has been updated successfully", "dataSourceCreatedTitle": "Data source created", "dataSourceCreatedMessage": "Your data source has been created successfully", "dataSourceInUseErrorTitle": "Error", "dataSourceInUseErrorMessage": "Data source is in use by a bot", "dataSourceDeletedTitle": "Data source deleted", "dataSourceDeletedMessage": "Your data source has been deleted successfully", "noDataFoundTitle": "Data not found", "noDataFoundMessage": "It seems you haven't created your first data source yet, proceed to create one using the button above.", "createDataSource": "Create data source", "column": {"datasourcesName": "Data source name", "datasourcesContent": "Data source content", "general.createdAt": "Created At", "general.actions": "Actions", "selectDatasource": "Select data source", "editDatasources": "Edit data source", "deleteDatasources": "Delete data source", "seeContent": "See content"}}, "botCreateWizard": {"dataSourceStep": {"title": "Bot - Data Source", "subtitle": "Select your data source."}, "identityStep": {"title": "Bot - Identity", "subtitle": "Bring your bot to life"}, "settingStep": {"title": "Bot - Configuration", "subtitle": "Configure your bot's colors."}, "dataSource": {"continueButton": "Continue"}, "identity": {"continueButton": "Continue"}, "colors": {"continueButton": "Continue"}}, "identity": {"botNameLabel": "Your bot's name", "botNamePlaceholder": "Give your bot a name", "descriptionLabel": "Describe your bot, who it is and what it does.", "descriptionExample": "Description example:", "descriptionCharacterCount": "Character count: {{characterCount}}.", "descriptionPlaceholder": "Describe your bot", "descriptionRemainingCharacters": "You can still write: {{remainingCharacters}} more characters.", "identityConfiguration": "Your bot's configuration", "urlLabel": "Url of your webpage where the bot will be integrated", "urlPlaceholder": "Add your bot's url", "urlExample": "Example: https://www.mywebsite.com", "enableChatbotLabel": "Enable chatbot", "continueButton": "Continue", "identityLength": "Define the character of your chatbot {{identityChecked}} / 3", "descriptionExampleContent": "The sales assistant is charismatic, persuasive, knowledgeable, and customer-oriented, always striving to provide exceptional service and meet the needs of clients."}, "color": {"customizeYourChatbot": "Customize your chatbot", "welcomeMessage": "Hello, how can I help?", "customerMessageBackgroundColor": "Customer Message Background Color", "customerMessageTextColor": "Customer Message Text Color", "selectAnIconForYourChatbot": "Select an icon for your chatbot", "recommendAnImage": "Recommend an image of 96x96px or smaller, in JPG or PNG format.", "customizeTheIconWhenTheBotIsActive": "Customize the icon when the bot is active.", "showWatermark": "Show watermark", "createMyBot": "Create my bot"}, "createDataSource": {"selectDataSourceType": "Select data source type", "fileTab": "File", "textTab": "Text"}, "fileDataSource": {"dataSourceNameLabel": "Name of your data source", "dataSourceNamePlaceholder": "Give a name to your data source", "fileFormatInfo": "Supported format is (.txt).", "maxCharactersInfo": "Maximum allowed characters is", "fileDropzoneMessage": "Drag files here or click to upload them.", "deleteButtonLabel": "Delete", "readingDocumentProgress": "Reading document…", "characterCountInfo": "Character count in the file:", "remainingCharactersInfo": "You can still add:", "previewDividerText": "File content preview", "textCutoffWarning": "Check that the text has not been cut off by the character limit.", "updateButtonLabel": "Update data source", "createButtonLabel": "Create data source"}, "user": {"deleteErrorAdmin": "You cannot delete the only administrator", "deleteError": "Employee could not be deleted", "deleteSuccess": "Employee deleted successfully", "createErrorExist": "User already exists with this email", "createError": "User could not be created", "createSuccess": "User created successfully", "editError": "User could not be updated", "editSuccess": "User updated successfully", "addUser": "Add user", "emptyDescription": "It seems you haven't created users for your team yet, proceed to create one using the button above.", "column": {"fullNameColumn": "Full Name", "roleColumn": "Role", "creationDateColumn": "Creation Date", "actionsColumn": "Actions", "deleteAction": "Delete", "editAction": "Edit"}, "sidebar": {"title": "New user", "roleLabel": "Collaborator's role *", "selectRolePlaceholder": "Select the collaborator's role", "fullNamePlaceholder": "Your collaborator's name"}}, "role": {"rolesList": "Roles List", "description": "A role provides access to predefined menus and functions based on the role assigned to an administrator who can access what they need.", "toastError": "Role could not be deleted", "toastSuccess": "Role deleted successfully", "roleCards": {"addNewRoleBtn": "Add New Role", "addRoleDescription": "Add a new role, if it doesn't exist", "cannotRevertAction": "This action cannot be reverted", "deleteRoleBtn": "Delete Role", "deleteRoleConfirmation": "Are you sure you want to delete the role?", "editRoleLink": "Edit Role", "enterRoleName": "Enter role name", "infoTooltip": "You will assign full access to the application.", "pleaseEnterValidRoleName": "Please enter a valid role name", "roleCreatedSuccessfully": "Role created successfully", "roleDeletedSuccessfully": "Role deleted successfully", "roleUpdatedSuccessfully": "Role updated successfully", "roleNameLabel": "Role Name", "rolePermissions": "Role Permissions", "selectAll": "Select all", "setRolePermissions": "Set role permissions", "updateRole": "Edit Role", "usersAssociated": "{{count}} users associated"}}, "teams": {"deleteTeamConfirmation": "Are you sure you want to delete this team?", "cannotRevertAction": "This action cannot be reverted", "deleteTeamBtn": "Delete Team", "createTeamSuccess": "Team created successfully", "deleteTeamError": "Team could not be deleted", "deleteTeamSuccess": "Team deleted successfully", "updateTeamError": "Team could not be updated", "updateTeamSuccess": "Team updated successfully", "teamCreatedSuccessfully": "Team created successfully", "teamDeletedSuccessfully": "Team deleted successfully", "teamNameLabel": "Team Name", "table": {"noDataTitle": "No data found", "noDataMessage": "It seems you haven't created any teams yet. Proceed to create one using the button above.", "createTeamButton": "Create team", "column": {"teamName": "Team Name", "creationDate": "Creation Date", "addUser": "Add user"}}, "addUserModal": {"search": "Search", "searchPlaceholder": "Search...", "userAddedSuccessfully": "User added to the team successfully", "failedToAddUser": "Could not add user to the team", "userRemovedSuccessfully": "User removed from the team successfully", "failedToRemoveUser": "Could not remove user from the team", "column": {"userName": "User Name", "role": "Role"}}, "sidebarNewUsers": {"title": "New team", "teamNameLabel": "Team Name", "teamNamePlaceholder": "Team Name", "teamNameRequired": "Team Name *", "defaultTeamLabel": "This team going to receive all new messages by default", "defaultSalesTeamLabel": "This team will receive all potential customers", "createButton": "Create", "editButton": "Edit", "cancelButton": "Cancel"}}, "company": {"companyTitle": "Your company:", "companyName": "Company name:", "principalActivity": "Principal activity:", "website": "Website:", "physicalAddress": "Physical address:", "email": "Email:", "companyDescription": "Describe your company:", "saveButton": "Save"}, "login": {"emailNotVerifiedToast": {"message": "Sorry, your email has not yet been verified. Please check your inbox and follow the instructions to verify your account."}, "resendVerificationEmailAlert": {"resendLink": "Resend email", "message": "Your email has not yet been verified. Please check your inbox and follow the instructions to verify your account. If you haven't received the email, you can resend it.", "successMessage": "Verification email sent successfully"}, "genericErrorToast": {"message": "Sorry, an error occurred during the login process. Please try again later or contact support for assistance."}, "loginTitle": "The future of your business starts here", "loginSubtitle": "Log in and discover the power of", "emailLabel": "Email", "passwordLabel": "Password", "forgotPasswordLink": "Forgot your password?", "rememberMeLabel": "Remember me", "loginButton": "Login to Biitbot", "newToBiitbotText": "New to Biitbot?", "createAccountLink": "Create an account", "invalidCredentials": "Email or password is incorrect"}, "register": {"termsAndConditions": "terms and conditions", "genericErrorMessage": "An error occurred. Please try again.", "duplicateEmailErrorMessage": "An account already exists with this email.", "registerTitle": "Adventure starts here 🚀", "registerSubtitle": "Make your app management easy and fun!", "fullnameLabel": "Full Name", "emailLabel": "Email", "passwordLabel": "Password", "acceptTermsLabel": "I agree to", "termsLink": "privacy policy & terms", "privacyPolicyLink": "privacy policy", "registerButton": "Sign up", "loginLinkText": "Already have an account?", "loginLink": "Sign in instead", "toastTermsError": "Please accept the terms and conditions", "passwordRequirementsError": "Please meet all password requirements", "passwordMinLength": "At least 8 characters long", "passwordUpperCase": "At least one uppercase letter", "passwordLowerCase": "At least one lowercase letter", "passwordNumber": "At least one number", "passwordSpecialChar": "At least one special character (!@#$%^&*(),.?\":{}|<>)"}, "forgotPassword": {"title": "Forgot your password? 🔒", "subtitle": "Enter your email and we'll send you a link to reset your password", "button": "Reset password", "backButton": "Back to login", "successAlert": "An email with instructions to reset your password has been sent"}, "order": {"loadingOrder": "Loading order...", "orderError": "Error", "orderLoadError": "Could not load the requested order.", "close": "Close", "editOrderTitle": "Edit Order #{{orderNumber}}", "errorUpdatingOrder": "Error updating order", "title": "Orders", "orders": "Order List", "orderNumber": "Order #", "totalAmount": "Total Amount", "createOrder": "Create Order", "editOrder": "Edit Order", "product": "Product", "selectProduct": "Select Product", "quantity": "Quantity", "addProduct": "Add Product", "total": "Total", "updating": "Updating...", "creating": "Creating...", "orderCreated": "Order created successfully", "orderUpdated": "Order updated successfully", "productNotAvailable": "Product not available", "products": "Products", "seeProducts": "View Products", "orderEmpty": "No orders yet", "noOrders": "No orders found", "noOrdersDescription": "Start by creating your first order", "createFirstOrder": "Create First Order", "deleteConfirm": "Delete Order", "deleteConfirmDescription": "Are you sure you want to delete this order? This action cannot be undone.", "deleteConfirmed": "Order deleted successfully!", "orderDeleteError": "Error deleting order", "orderDeleteErrorDescription": "There was an error deleting the order. Please try again.", "status": "Status", "pending": "Pending", "confirmed": "Confirmed", "preparing": "Preparing", "in_transit": "In Transit", "delivered": "Delivered", "canceled": "Canceled", "activeOrders": "Active Orders", "activeOrdersSubtitle": "View and manage your active orders", "completedOrders": "Completed Orders", "completedOrdersSubtitle": "View your completed and canceled orders", "noActiveOrders": "No active orders found", "noCompletedOrders": "No completed orders found", "statusUpdated": "Order status updated to: {{status}}", "statusUpdateError": "Error updating order status", "errorIncompleteFields": "Please fill in all required fields", "errorSavingOrder": "Error saving order", "errorLoadingProducts": "Error loading products"}, "termsAndConditions": {"title": "Terms and Conditions", "sections": [{"title": "Introduction", "content": ["Biitbot is an artificial intelligence chatbot platform designed to provide intelligent support to businesses' customers or website users.", "The main purpose of Biitbot is to enable businesses to quickly and efficiently address their customers' queries through chatbots on their websites, among other platforms."]}, {"title": "Using the platform", "content": ["Anyone interested in providing intelligent support to their customers through a chatbot on their website can use the Biitbot platform.", "There are no eligibility requirements to use the Biitbot platform.", "When registering on the platform, users must provide their name, email, password, phone, and basic company information.", "Users must refrain from any activity that constitutes misuse of the platform, such as sending spam or inappropriate content."]}, {"title": "Intellectual property", "content": ["All intellectual property rights in the Biitbot platform and its related content belong exclusively to Biitbot.", "Users are not permitted to use, copy, or modify the content of the platform without Biitbot's express consent."]}, {"title": "User responsibilities", "content": ["Users are prohibited from using the platform to send spam, inappropriate content, or other unlawful activities.", "Users are responsible for providing accurate information to the chatbot so it can provide accurate responses to customers.", "Users are responsible for using the platform appropriately and must not engage in activities that negatively impact the customer experience.", "Users are also responsible for auditing conversations with customers to help the chatbot learn from its mistakes and improve its performance."]}, {"title": "Privacy and data protection", "content": ["Biitbot collects personal information such as email, password, phone, and basic company information for chatbot training purposes.", "The personal information provided by users is securely stored in a database and protected through encryption.", "Biitbot does not use cookies or other tracking technologies to collect user data."]}, {"title": "Limitation of liability", "content": ["Biitbot is not liable for any errors the chatbot may make, as it learns from its own mistakes and improves over time.", "Biitbot is committed to providing the best possible service to its customers through chatbot training with information provided by users.", "Damages caused by the chatbot to businesses' customers are excluded."]}, {"title": "Modifications and termination of service", "content": ["Biitbot reserves the right to make modifications to the terms and conditions at any time.", "Users will be notified by email about modifications to the terms and conditions.", "The company may terminate the service if the user fails to comply with the terms and conditions set forth in this document."]}, {"title": "Applicable law and jurisdiction", "content": ["The terms and conditions are governed by the laws of the Dominican Republic.", "Any legal disputes arising from the terms and conditions will be resolved within the jurisdiction of the Dominican Republic."]}, {"title": "General provisions", "content": ["These terms and conditions constitute the entire agreement between Biitbot and its users and supersede all prior agreements and understandings.", "If any provision of these terms and conditions is deemed invalid or unenforceable, the remaining provisions will continue in full force and effect.", "Biitbot's failure to enforce any provision of these terms and conditions does not constitute a waiver of that provision."]}]}, "product": {"viewDescription": "View Description", "noDescription": "No description available", "products": "Products", "product": "Product", "price": "Price", "quantity": "Quantity", "total": "Total", "empty": "Without products", "createProduct": "Create product", "editProduct": "Edit product", "createProductFromImage": "Create product from image", "createProductManual": "Create product manually", "name": "Name", "description": "Description", "activeProduct": "Active product", "createProductSuccess": "Product created successfully", "enabled": "Enabled", "disabled": "Disabled"}, "Profesional": "Professional", "Debe comportarse con un tono formal y proporcionar respuestas claras y concisas. Adecuado para ambientes de negocio serios.": "Must behave with a formal tone and provide clear and concise responses. Suitable for serious business environments.", "Divertido": "Entertaining", "Debe usar humor en sus respuestas y hacer bromas de vez en cuando. Adecuado para empresas con una imagen de marca relajada y divertida.": "Must use humor in their responses and make jokes occasionally. Suitable for companies with relaxed and exciting brand images.", "Empático": "Empathy", "Debe mostrar mucha empatía y comprensión en sus respuestas. Adecuado para servicios de atención al cliente o empresas en el sector de la salud.": "Must show a lot of empathy and comprehension in their responses. Suitable for customer service or health care companies.", "Asesor": "Consultant", "Debe proporcionar recomendaciones basadas en la información que recopile. Adecuado para tiendas online o servicios personalizados.": "Must provide recommendations based on the information that they have collected. Suitable for online stores or personalized services.", "Amigable": "Friendly", "Debe tener un tono más casual y amigable. Debería usar un lenguaje más informal e incluir emojis en sus respuestas.": "Must have a more casual and friendly tone. Should use an informal language and include emojis in their responses.", "Instructivo": "Helpful", "Debe centrarse en proporcionar explicaciones claras y detalladas. Adecuado para productos o servicios que requieran mucha educación o soporte al cliente.": "Must focus on providing clear and detailed explanations. Suitable for products or services that require a lot of education or customer support.", "Innovador": "Innovative", "Debe utilizar lenguaje de vanguardia y estar al tanto de las últimas tendencias. Adecuado para empresas de tecnología o startups.": "Must use the latest language and be aware of the latest trends. Suitable for tech giants or startups.", "ver role": "View Role", "actualizar role": "Update Role", "crear role": "Create Role", "eliminar role": "Delete Role", "responder chat": "Answer Chat", "borrar chat": "Delete Chat", "ver chat": "View Chat", "ver permisos": "View Permissions", "asignar permiso a role": "Assign Permission to Role", "eliminar permiso a role": "<PERSON><PERSON> Permission from Role", "crear usuario": "Create User", "asignar role a usuario": "Assign Role to User", "eliminar usuario": "Delete User", "ver usuario": "View User", "eliminar role a usuario": "Revoke Role from User", "actualizar usuario": "Update User", "crear bot": "Create <PERSON><PERSON>", "borrar bot": "Delete Bot", "actualizar bot": "Update Bot", "leer bot": "<PERSON>", "eliminar empresa": "Delete Company", "actualizar informacion de la empresa": "Update Company Information", "ver informacion de la empresa": "View Company Information", "ver estadisticas": "View Statistics", "conectado": "Connected", "desconectado": "Disconnected", "userProfile": {"about": "About", "noDescription": "No description", "personalInfo": "Personal Information", "noEmail": "No email", "noPhone": "No phone number", "availability": "Mon - Fri 10AM - 8PM", "options": "Options", "addTag": "Add Tag", "importantContact": "Important Contact", "sharedMedia": "Shared Media", "deleteContact": "Delete Contact", "blockContact": "Block Contact"}, "dateFormats": {"short": "MM/DD/YYYY hh:mm A", "long": "dddd, MMMM D, YYYY hh:mm A"}, "footer": {"text": "All rights reserved"}, "leadCenter": {"editLeadTitle": "Edit lead center", "editModal": {"title": "Configure your chatbot to detect leads!", "subtitle": "These fields will be used by the artificial intelligence to detect leads.", "step1": "Describe the characteristics or attributes that the system will use to detect a lead in conversations.", "step1InputPlaceholder": "Those people interested in buying new or used vehicles, etc.", "step2": "Specify the fields you want our AI to capture during conversations with leads."}}, "integrations": {"instagram": {"connect": "Connect Instagram account", "change": "Change Instagram account"}}, "facebookIntegration": {"instagramIntegration": "Instagram Integration", "connectInstagramAccount": "Connect your Instagram account to manage messages.", "activeInstagramAccounts": "Active Instagram Accounts:", "linkAnotherInstagramAccount": "<PERSON> Another Instagram Account", "connectWithInstagram": "Connect with Instagram", "noActiveIntegrationFound": "No active integration found.", "integrationUnlinked": "Integration successfully unlinked.", "errorUnlinkingIntegration": "Error unlinking the integration.", "errorLoadingIntegration": "Error loading the integration.", "activeAccountUpdated": "Active account updated.", "errorUpdatingActiveAccount": "Error updating the active account.", "unlinkAccount": "Unlink account", "selectAccount": "Select an account", "connecting": "Connecting...", "loading": "Loading...", "confirmUnlink": "Confirm Account Unlink", "confirmUnlinkMessage": "Are you sure you want to unlink this Instagram account? This will stop message management for this account.", "status": {"connected": "Connected", "disconnected": "Disconnected", "connecting": "Connecting...", "error": "Connection Error", "warning": "Connection Warning", "pending": "Pending Connection", "inactive": "Inactive", "lastSync": "Last sync", "messages": "Messages"}}, "in progress": "In Progress", "done": "Converted", "Equipos": "Teams", "Empresa": "Company", "Usuarios": "Users", "Configuración": "Settings", "Fuentes de datos": "Data Sources", "Lista de bots": "Bot List", "Contactos": "Contacts", "Conversaciones": "Conversations", "menu": {"hub": "<PERSON><PERSON>", "conversations": "Conversations", "contacts": "Contacts", "bot": "Bot", "botList": "Bot List", "dataSources": "Data Sources", "order": "Order", "orderList": "Order List", "products": "Products", "settings": "Settings", "users": "Users", "company": "Company"}}