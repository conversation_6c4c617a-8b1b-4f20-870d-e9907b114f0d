import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "reactstrap";
import { useTranslation } from "react-i18next";
import Select from "react-select";
import { Instagram, Plus, X, AlertCircle } from "react-feather";

import { useFacebookAuth } from "../../../../hooks/useFacebookAuth";
import ConfirmationModal from "../../common/ConfirmationModal";
import ConnectionStatus, {
  DetailedConnectionStatus,
} from "../../common/ConnectionStatus";
import ErrorDisplay from "../../common/ErrorBoundary";

const FacebookAuth = ({ botRequestId }) => {
  const { t } = useTranslation();

  // Local state for confirmation modal
  const [confirmModal, setConfirmModal] = useState({
    isOpen: false,
    accountToUnlink: null,
    isUnlinking: false,
  });

  // Use custom hook for Facebook Auth logic
  const {
    selectedAccountOption,
    linkedAccount,
    filteredChatAccountOptions,
    isLoading,
    isConnecting,
    error,
    errorType,
    handleAccountSelectionChange,
    handleFacebookLogin,
    handleUnlinkIntegration,
    clearError,
    refreshIntegrations,
    hasIntegrations,
    hasChatAccounts,
  } = useFacebookAuth(botRequestId);

  // Handle unlink confirmation
  const handleUnlinkClick = (account) => {
    setConfirmModal({
      isOpen: true,
      accountToUnlink: account,
      isUnlinking: false,
    });
  };

  // Handle confirmed unlink
  const handleConfirmedUnlink = async () => {
    if (!confirmModal.accountToUnlink) return;

    setConfirmModal((prev) => ({ ...prev, isUnlinking: true }));

    try {
      await handleUnlinkIntegration(confirmModal.accountToUnlink.id);
      setConfirmModal({
        isOpen: false,
        accountToUnlink: null,
        isUnlinking: false,
      });
    } catch (error) {
      setConfirmModal((prev) => ({ ...prev, isUnlinking: false }));
    }
  };

  // Close confirmation modal
  const closeConfirmModal = () => {
    if (!confirmModal.isUnlinking) {
      setConfirmModal({
        isOpen: false,
        accountToUnlink: null,
        isUnlinking: false,
      });
    }
  };

  // Get connection status for account
  const getConnectionStatus = (account) => {
    if (!account) return "disconnected";
    if (account.isActive === false) return "warning";
    if (account.hasError) return "error";
    return "connected";
  };

  // Render enhanced error display
  const renderError = () => {
    if (!error) return null;

    return (
      <ErrorDisplay
        error={error}
        errorType={errorType}
        onRetry={refreshIntegrations}
        onDismiss={clearError}
        showRetry={true}
        showDismiss={true}
      />
    );
  };

  // Render loading spinner
  const renderLoadingSpinner = () => {
    if (!isLoading) return null;

    return (
      <div className="d-flex justify-content-center mb-3">
        <Spinner size="sm" color="primary" />
        <span className="ms-2">{t("general.loading")}</span>
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <h4>{t("facebookIntegration.instagramIntegration")}</h4>
      </CardHeader>
      <CardBody className="text-center">
        {renderError()}
        {renderLoadingSpinner()}

        <p>{t("facebookIntegration.connectInstagramAccount")}</p>

        {hasChatAccounts ? (
          <div className="d-flex flex-column align-items-center mb-3">
            <div className="w-100 mb-3" style={{ maxWidth: "400px" }}>
              {linkedAccount
                .filter((option) => option.type === "chat")
                .map((option) => (
                  <div key={option.id || option.value} className="card mb-2">
                    <div className="card-body p-3">
                      <div className="d-flex justify-content-between align-items-start">
                        <div className="flex-grow-1">
                          <div className="d-flex align-items-center mb-2">
                            <ConnectionStatus
                              status={getConnectionStatus(option)}
                              size="sm"
                            />
                            <span className="ms-2 fw-bold">
                              {option.accountName}
                            </span>
                          </div>
                          <small className="text-muted">
                            ID: {option.accountId}
                          </small>
                          {option.lastSync && (
                            <div className="text-muted small mt-1">
                              {t("facebookIntegration.status.lastSync")}:{" "}
                              {option.lastSync}
                            </div>
                          )}
                        </div>
                        <button
                          className="btn btn-outline-danger btn-sm"
                          onClick={() => handleUnlinkClick(option)}
                          disabled={isLoading || confirmModal.isUnlinking}
                          title={t("facebookIntegration.unlinkAccount")}
                        >
                          <X size={16} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
            </div>

            <div
              className="d-flex gap-2 align-items-center justify-content-center w-100 mb-3"
              style={{ maxWidth: "400px" }}
            >
              <Select
                className="react-select-container flex-grow-1"
                classNamePrefix="react-select"
                options={filteredChatAccountOptions}
                value={selectedAccountOption}
                onChange={(selectedOption) =>
                  handleAccountSelectionChange(selectedOption, false)
                }
                isClearable={false}
                isDisabled={isLoading}
                placeholder={t("facebookIntegration.selectAccount")}
              />
            </div>

            <Button
              color="info"
              onClick={handleFacebookLogin}
              disabled={isLoading || isConnecting}
            >
              {isConnecting ? (
                <>
                  <Spinner size="sm" className="me-2" />
                  {t("facebookIntegration.connecting")}
                </>
              ) : (
                <>
                  <Plus size={22} className="me-2" />
                  {t("facebookIntegration.linkAnotherInstagramAccount")}
                </>
              )}
            </Button>
          </div>
        ) : (
          <Button
            color="primary"
            onClick={handleFacebookLogin}
            disabled={isLoading || isConnecting}
            size="lg"
          >
            {isConnecting ? (
              <>
                <Spinner size="sm" className="me-2" />
                {t("facebookIntegration.connecting")}
              </>
            ) : (
              <>
                <Instagram size={22} className="me-2" />
                {t("facebookIntegration.connectWithInstagram")}
              </>
            )}
          </Button>
        )}

        {!hasIntegrations && !isLoading && (
          <p className="mt-2 text-muted">
            {t("facebookIntegration.noActiveIntegrationFound")}
          </p>
        )}
      </CardBody>

      {/* Confirmation Modal for Unlinking */}
      <ConfirmationModal
        isOpen={confirmModal.isOpen}
        toggle={closeConfirmModal}
        onConfirm={handleConfirmedUnlink}
        title={t("facebookIntegration.confirmUnlink")}
        message={
          confirmModal.accountToUnlink
            ? `${t("facebookIntegration.confirmUnlinkMessage")} (${
                confirmModal.accountToUnlink.accountName
              })`
            : t("facebookIntegration.confirmUnlinkMessage")
        }
        confirmText={t("facebookIntegration.unlinkAccount")}
        cancelText={t("general.cancel")}
        variant="danger"
        isLoading={confirmModal.isUnlinking}
      />
    </Card>
  );
};

export default FacebookAuth;
