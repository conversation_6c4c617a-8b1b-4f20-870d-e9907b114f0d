import React from "react";
import { <PERSON><PERSON>, Spinner } from "reactstrap";
import { useTranslation } from "react-i18next";
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Clock,
  Wifi,
  WifiOff 
} from "react-feather";

/**
 * Connection status indicator component
 * @param {object} props - Component props
 * @param {string} props.status - Connection status (connected, disconnected, connecting, error, warning)
 * @param {string} props.message - Custom status message
 * @param {boolean} props.showIcon - Whether to show status icon
 * @param {boolean} props.showText - Whether to show status text
 * @param {string} props.size - Badge size (sm, md, lg)
 * @param {boolean} props.animated - Whether to animate connecting state
 * @returns {JSX.Element} ConnectionStatus component
 */
const ConnectionStatus = ({
  status = "disconnected",
  message,
  showIcon = true,
  showText = true,
  size = "md",
  animated = true,
}) => {
  const { t } = useTranslation();

  // Status configuration
  const statusConfig = {
    connected: {
      color: "success",
      icon: CheckCircle,
      text: message || t("facebookIntegration.status.connected"),
      bgColor: "success",
    },
    disconnected: {
      color: "secondary",
      icon: WifiOff,
      text: message || t("facebookIntegration.status.disconnected"),
      bgColor: "secondary",
    },
    connecting: {
      color: "primary",
      icon: animated ? Spinner : Wifi,
      text: message || t("facebookIntegration.status.connecting"),
      bgColor: "primary",
    },
    error: {
      color: "danger",
      icon: XCircle,
      text: message || t("facebookIntegration.status.error"),
      bgColor: "danger",
    },
    warning: {
      color: "warning",
      icon: AlertCircle,
      text: message || t("facebookIntegration.status.warning"),
      bgColor: "warning",
    },
    pending: {
      color: "info",
      icon: Clock,
      text: message || t("facebookIntegration.status.pending"),
      bgColor: "info",
    },
  };

  const config = statusConfig[status] || statusConfig.disconnected;
  const IconComponent = config.icon;

  // Size mapping
  const sizeMap = {
    sm: { iconSize: 12, className: "badge-sm" },
    md: { iconSize: 14, className: "" },
    lg: { iconSize: 16, className: "badge-lg" },
  };

  const sizeConfig = sizeMap[size] || sizeMap.md;

  return (
    <Badge 
      color={config.color} 
      className={`d-inline-flex align-items-center ${sizeConfig.className}`}
      style={{ gap: "0.25rem" }}
    >
      {showIcon && (
        <IconComponent 
          size={sizeConfig.iconSize} 
          className={status === "connecting" && animated ? "spin" : ""}
        />
      )}
      {showText && <span>{config.text}</span>}
    </Badge>
  );
};

/**
 * Enhanced connection status with additional details
 * @param {object} props - Component props
 * @param {string} props.status - Connection status
 * @param {string} props.accountName - Connected account name
 * @param {string} props.lastSync - Last sync time
 * @param {number} props.messageCount - Number of messages
 * @param {boolean} props.isActive - Whether integration is active
 * @returns {JSX.Element} DetailedConnectionStatus component
 */
export const DetailedConnectionStatus = ({
  status,
  accountName,
  lastSync,
  messageCount,
  isActive = true,
}) => {
  const { t } = useTranslation();

  return (
    <div className="connection-status-detailed">
      <div className="d-flex align-items-center justify-content-between mb-2">
        <div className="d-flex align-items-center">
          <ConnectionStatus status={status} size="sm" />
          {accountName && (
            <span className="ms-2 fw-bold">{accountName}</span>
          )}
        </div>
        {!isActive && (
          <Badge color="warning" className="ms-2">
            {t("facebookIntegration.status.inactive")}
          </Badge>
        )}
      </div>
      
      {(lastSync || messageCount !== undefined) && (
        <div className="text-muted small">
          {lastSync && (
            <div>
              {t("facebookIntegration.status.lastSync")}: {lastSync}
            </div>
          )}
          {messageCount !== undefined && (
            <div>
              {t("facebookIntegration.status.messages")}: {messageCount}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ConnectionStatus;
