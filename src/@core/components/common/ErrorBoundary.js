import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, CardBody } from "reactstrap";
import { useTranslation } from "react-i18next";
import { 
  <PERSON>ert<PERSON>riangle, 
  Refresh<PERSON>w, 
  ExternalLink, 
  Settings,
  Wifi,
  Clock
} from "react-feather";

/**
 * Enhanced error display component with specific error types and solutions
 * @param {object} props - Component props
 * @param {string} props.error - Error message or type
 * @param {string} props.errorType - Type of error (network, auth, config, etc.)
 * @param {function} props.onRetry - Function to retry the failed operation
 * @param {function} props.onDismiss - Function to dismiss the error
 * @param {boolean} props.showRetry - Whether to show retry button
 * @param {boolean} props.showDismiss - Whether to show dismiss button
 * @param {object} props.additionalInfo - Additional error information
 * @returns {JSX.Element} ErrorDisplay component
 */
const ErrorDisplay = ({
  error,
  errorType = "general",
  onRetry,
  onDismiss,
  showRetry = true,
  showDismiss = true,
  additionalInfo = {},
}) => {
  const { t } = useTranslation();

  // Error type configurations
  const errorConfigs = {
    network: {
      icon: Wifi,
      color: "warning",
      title: t("errors.network.title"),
      message: t("errors.network.message"),
      suggestions: [
        t("errors.network.checkConnection"),
        t("errors.network.tryAgain"),
      ],
    },
    authentication: {
      icon: Settings,
      color: "danger",
      title: t("errors.auth.title"),
      message: t("errors.auth.message"),
      suggestions: [
        t("errors.auth.reauthorize"),
        t("errors.auth.checkPermissions"),
      ],
    },
    configuration: {
      icon: Settings,
      color: "warning",
      title: t("errors.config.title"),
      message: t("errors.config.message"),
      suggestions: [
        t("errors.config.checkSettings"),
        t("errors.config.contactSupport"),
      ],
    },
    timeout: {
      icon: Clock,
      color: "info",
      title: t("errors.timeout.title"),
      message: t("errors.timeout.message"),
      suggestions: [
        t("errors.timeout.tryAgain"),
        t("errors.timeout.checkStatus"),
      ],
    },
    rateLimit: {
      icon: Clock,
      color: "warning",
      title: t("errors.rateLimit.title"),
      message: t("errors.rateLimit.message"),
      suggestions: [
        t("errors.rateLimit.waitAndRetry"),
        t("errors.rateLimit.reduceRequests"),
      ],
    },
    general: {
      icon: AlertTriangle,
      color: "danger",
      title: t("errors.general.title"),
      message: error || t("errors.general.message"),
      suggestions: [
        t("errors.general.tryAgain"),
        t("errors.general.contactSupport"),
      ],
    },
  };

  const config = errorConfigs[errorType] || errorConfigs.general;
  const IconComponent = config.icon;

  return (
    <Alert color={config.color} className="mb-3">
      <div className="d-flex align-items-start">
        <IconComponent size={20} className="me-2 mt-1 flex-shrink-0" />
        <div className="flex-grow-1">
          <h6 className="alert-heading mb-2">{config.title}</h6>
          <p className="mb-2">{config.message}</p>
          
          {config.suggestions.length > 0 && (
            <div className="mb-3">
              <small className="fw-bold">{t("errors.suggestions")}:</small>
              <ul className="mb-0 mt-1">
                {config.suggestions.map((suggestion, index) => (
                  <li key={index}>
                    <small>{suggestion}</small>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {additionalInfo.details && (
            <details className="mb-2">
              <summary className="text-muted small cursor-pointer">
                {t("errors.technicalDetails")}
              </summary>
              <pre className="small text-muted mt-2 mb-0">
                {JSON.stringify(additionalInfo.details, null, 2)}
              </pre>
            </details>
          )}

          <div className="d-flex gap-2 mt-2">
            {showRetry && onRetry && (
              <Button
                color={config.color}
                size="sm"
                onClick={onRetry}
                outline
              >
                <RefreshCw size={14} className="me-1" />
                {t("errors.retry")}
              </Button>
            )}
            
            {showDismiss && onDismiss && (
              <Button
                color="secondary"
                size="sm"
                onClick={onDismiss}
                outline
              >
                {t("general.close")}
              </Button>
            )}

            {errorType === "configuration" && (
              <Button
                color="info"
                size="sm"
                onClick={() => window.open("/settings", "_blank")}
                outline
              >
                <ExternalLink size={14} className="me-1" />
                {t("errors.openSettings")}
              </Button>
            )}
          </div>
        </div>
      </div>
    </Alert>
  );
};

/**
 * Error boundary component for catching React errors
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo,
    });

    // Log error to console in development
    if (process.env.NODE_ENV === "development") {
      console.error("ErrorBoundary caught an error:", error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <Card className="border-danger">
          <CardBody>
            <ErrorDisplay
              error={this.state.error?.message}
              errorType="general"
              onRetry={() => {
                this.setState({ hasError: false, error: null, errorInfo: null });
                window.location.reload();
              }}
              additionalInfo={{
                details: {
                  error: this.state.error?.toString(),
                  stack: this.state.error?.stack,
                  componentStack: this.state.errorInfo?.componentStack,
                },
              }}
            />
          </CardBody>
        </Card>
      );
    }

    return this.props.children;
  }
}

export default ErrorDisplay;
export { ErrorBoundary };
