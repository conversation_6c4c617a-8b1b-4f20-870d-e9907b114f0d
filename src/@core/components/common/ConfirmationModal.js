import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from "reactstrap";
import { useTranslation } from "react-i18next";
import { AlertTriangle, X, Check } from "react-feather";

/**
 * Reusable confirmation modal component
 * @param {object} props - Component props
 * @param {boolean} props.isOpen - Whether modal is open
 * @param {function} props.toggle - Function to toggle modal
 * @param {function} props.onConfirm - Function to call on confirmation
 * @param {string} props.title - Modal title
 * @param {string} props.message - Confirmation message
 * @param {string} props.confirmText - Confirm button text
 * @param {string} props.cancelText - Cancel button text
 * @param {string} props.variant - Modal variant (danger, warning, info)
 * @param {boolean} props.isLoading - Whether action is loading
 * @param {object} props.icon - Custom icon component
 * @returns {JSX.Element} ConfirmationModal component
 */
const ConfirmationModal = ({
  isOpen = false,
  toggle,
  onConfirm,
  title,
  message,
  confirmText,
  cancelText,
  variant = "danger",
  isLoading = false,
  icon: CustomIcon,
}) => {
  const { t } = useTranslation();

  // Default texts if not provided
  const defaultTitle = title || t("general.confirm");
  const defaultMessage = message || t("general.confirmAction");
  const defaultConfirmText = confirmText || t("general.yes");
  const defaultCancelText = cancelText || t("general.cancel");

  // Icon mapping based on variant
  const getIcon = () => {
    if (CustomIcon) return <CustomIcon size={24} />;
    
    switch (variant) {
      case "danger":
        return <AlertTriangle size={24} className="text-danger" />;
      case "warning":
        return <AlertTriangle size={24} className="text-warning" />;
      case "info":
        return <Check size={24} className="text-info" />;
      default:
        return <AlertTriangle size={24} className="text-danger" />;
    }
  };

  // Button color mapping
  const getButtonColor = () => {
    switch (variant) {
      case "danger":
        return "danger";
      case "warning":
        return "warning";
      case "info":
        return "primary";
      default:
        return "danger";
    }
  };

  const handleConfirm = async () => {
    if (onConfirm && !isLoading) {
      await onConfirm();
    }
  };

  const handleCancel = () => {
    if (!isLoading && toggle) {
      toggle();
    }
  };

  return (
    <Modal 
      isOpen={isOpen} 
      toggle={handleCancel}
      centered
      backdrop={isLoading ? "static" : true}
      keyboard={!isLoading}
    >
      <ModalHeader toggle={!isLoading ? handleCancel : undefined}>
        <div className="d-flex align-items-center">
          {getIcon()}
          <span className="ms-2">{defaultTitle}</span>
        </div>
      </ModalHeader>
      
      <ModalBody>
        <p className="mb-0">{defaultMessage}</p>
        {variant === "danger" && (
          <small className="text-muted mt-2 d-block">
            {t("general.actionCannotBeUndone")}
          </small>
        )}
      </ModalBody>
      
      <ModalFooter>
        <Button
          color="secondary"
          onClick={handleCancel}
          disabled={isLoading}
          outline
        >
          {defaultCancelText}
        </Button>
        
        <Button
          color={getButtonColor()}
          onClick={handleConfirm}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Spinner size="sm" className="me-2" />
              {t("general.processing")}
            </>
          ) : (
            defaultConfirmText
          )}
        </Button>
      </ModalFooter>
    </Modal>
  );
};

export default ConfirmationModal;
