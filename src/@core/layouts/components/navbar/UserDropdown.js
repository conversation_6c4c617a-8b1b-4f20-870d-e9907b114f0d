import { Link } from "react-router-dom";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { Power } from "react-feather";
import {
  UncontrolledDropdown,
  DropdownMenu,
  DropdownToggle,
  DropdownItem,
} from "reactstrap";

import Avatar from "@components/avatar";
import { isUserLoggedIn } from "@utils";
import { handleLogout } from "@store/authentication";
import { getUserData } from "../../../../auth/utils";
import { useTranslation } from "react-i18next";
import { authRequest } from "../../../../services";

const UserDropdown = () => {
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const [userData, setUserData] = useState(null);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  useEffect(() => {
    if (isUserLoggedIn() !== null) {
      setUserData(getUserData());
    }
  }, []);

  const handleLogoutClick = async () => {
    setIsLoggingOut(true);
    try {
      // Llamar al nuevo endpoint de logout del backend
      await authRequest.logout();

      // Limpiar datos de autenticación
      authRequest.clearAuthData();

      // Usar el logout del store de Redux
      dispatch(handleLogout());

      // Redirigir al login
      window.location.href = "/login";
    } catch (error) {
      console.error("Error durante logout:", error);

      // Incluso si falla, limpiar datos locales y redirigir
      authRequest.clearAuthData();
      dispatch(handleLogout());
      window.location.href = "/login";
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <UncontrolledDropdown tag="li" className="dropdown-user nav-item">
      <DropdownToggle
        href="/"
        tag="a"
        className="nav-link dropdown-user-link"
        onClick={(e) => e.preventDefault()}
      >
        <div className="user-nav d-sm-flex d-none">
          <span className="user-name fw-bold">
            {userData && userData?.name?.toUpperCase()}
          </span>
        </div>
        <Avatar
          initials
          className="me-1"
          color={"light-primary"}
          content={(userData && userData?.name?.toUpperCase()) || "none"}
        />
      </DropdownToggle>
      <DropdownMenu end>
        <DropdownItem onClick={handleLogoutClick} disabled={isLoggingOut}>
          <Power size={14} className="me-75" />
          <span className="align-middle">
            {isLoggingOut ? t("general.loggingOut") : t("general.logOff")}
          </span>
        </DropdownItem>
      </DropdownMenu>
    </UncontrolledDropdown>
  );
};

export default UserDropdown;
