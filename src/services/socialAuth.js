import { del, get, post, put } from "./request";

// Circuit breaker configuration
const CIRCUIT_BREAKER_CONFIG = {
  failureThreshold: 5, // Number of failures before opening circuit
  resetTimeout: 30000, // Time to wait before trying again (30 seconds)
  monitoringPeriod: 60000, // Time window for monitoring failures (1 minute)
};

// Circuit breaker state
let circuitBreakerState = {
  failures: 0,
  lastFailureTime: null,
  state: "CLOSED", // CLOSED, OPEN, HALF_OPEN
};

/**
 * Circuit breaker implementation
 * @param {Function} operation - The operation to execute
 * @param {string} operationName - Name of the operation for logging
 * @returns {Promise} Result of the operation
 */
const executeWithCircuitBreaker = async (
  operation,
  operationName = "API Call"
) => {
  const now = Date.now();

  // Check if circuit is open
  if (circuitBreakerState.state === "OPEN") {
    if (
      now - circuitBreakerState.lastFailureTime <
      CIRCUIT_BREAKER_CONFIG.resetTimeout
    ) {
      throw new Error(
        `Circuit breaker is OPEN for ${operationName}. Try again later.`
      );
    } else {
      // Try to close circuit (half-open state)
      circuitBreakerState.state = "HALF_OPEN";
    }
  }

  try {
    const result = await operation();

    // Success - reset circuit breaker
    if (circuitBreakerState.state === "HALF_OPEN") {
      circuitBreakerState.state = "CLOSED";
      circuitBreakerState.failures = 0;
      circuitBreakerState.lastFailureTime = null;
    }

    return result;
  } catch (error) {
    // Failure - update circuit breaker state
    circuitBreakerState.failures++;
    circuitBreakerState.lastFailureTime = now;

    if (
      circuitBreakerState.failures >= CIRCUIT_BREAKER_CONFIG.failureThreshold
    ) {
      circuitBreakerState.state = "OPEN";
      console.warn(
        `Circuit breaker opened for ${operationName} after ${circuitBreakerState.failures} failures`
      );
    }

    throw error;
  }
};

/**
 * Retry logic with exponential backoff
 * @param {Function} operation - The operation to retry
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} baseDelay - Base delay in milliseconds
 * @param {string} operationName - Name of the operation for logging
 * @returns {Promise} Result of the operation
 */
const retryWithBackoff = async (
  operation,
  maxRetries = 3,
  baseDelay = 1000,
  operationName = "API Call"
) => {
  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;

      // Don't retry on certain HTTP status codes
      if (
        error.response &&
        [400, 401, 403, 404, 422].includes(error.response.status)
      ) {
        throw error;
      }

      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break;
      }

      // Calculate delay with exponential backoff and jitter
      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
      console.warn(
        `${operationName} failed (attempt ${attempt + 1}/${
          maxRetries + 1
        }). Retrying in ${Math.round(delay)}ms...`,
        error.message
      );

      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw lastError;
};

/**
 * Enhanced API call wrapper with circuit breaker and retry logic
 * @param {Function} apiCall - The API call function
 * @param {string} operationName - Name of the operation
 * @param {object} options - Configuration options
 * @returns {Promise} Result of the API call
 */
const enhancedApiCall = async (apiCall, operationName, options = {}) => {
  const {
    useCircuitBreaker = true,
    useRetry = true,
    maxRetries = 3,
    baseDelay = 1000,
  } = options;

  const operation = useRetry
    ? () => retryWithBackoff(apiCall, maxRetries, baseDelay, operationName)
    : apiCall;

  return useCircuitBreaker
    ? executeWithCircuitBreaker(operation, operationName)
    : operation();
};

// Enhanced API functions with circuit breaker and retry logic

export const addSocialIntegration = (chatProvider, data) => {
  return enhancedApiCall(
    () => post(`/social-auth/${chatProvider}/continue`, data),
    `Add Social Integration (${chatProvider})`,
    { maxRetries: 2 }
  );
};

export const loginWithGoogle = (botRequestId, userId) => {
  return enhancedApiCall(
    () => post("/social-auth/google/auth", { botRequestId, userId }),
    "Google Login",
    { maxRetries: 2 }
  );
};

export const getAllIntegration = () => {
  return enhancedApiCall(() => get("/social-auth"), "Get All Integrations");
};

export const deleteIntegration = (id) => {
  return enhancedApiCall(
    () => del(`/social-auth/integration/${id}`),
    "Delete Integration",
    { maxRetries: 1 } // Less retries for delete operations
  );
};

export const unlinkIntegration = (id) => {
  return enhancedApiCall(
    () => put(`/social-auth/integration/${id}/unlink`),
    "Unlink Integration",
    { maxRetries: 2 }
  );
};

export const getIntegrationById = (id) => {
  return enhancedApiCall(
    () => get(`/social-auth/${id}`),
    "Get Integration By ID"
  );
};

export const getIntegration = () => {
  return enhancedApiCall(
    () => get(`/social-auth/botCompany`),
    "Get Bot Company Integration"
  );
};

export const getIntegrationByBotRequestId = (botRequestId) => {
  return enhancedApiCall(
    () => get(`/social-auth/integration/botCompany/${botRequestId}`),
    "Get Integration By Bot Request ID"
  );
};

export const updateSelectedSocialAuth = (data) => {
  return enhancedApiCall(
    () => put("/social-auth/integration/select", data),
    "Update Selected Social Auth",
    { maxRetries: 2 }
  );
};

export const getGoogleAuthUrl = (botRequestId) => {
  return enhancedApiCall(
    () => get(`/social-auth/google/${botRequestId}/auth-url`),
    "Get Google Auth URL"
  );
};

// New Facebook/Instagram specific functions

export const validateFacebookIntegration = (integrationId) => {
  return enhancedApiCall(
    () => get(`/social-auth/facebook/validate/${integrationId}`),
    "Validate Facebook Integration"
  );
};

export const refreshFacebookToken = (integrationId) => {
  return enhancedApiCall(
    () => post(`/social-auth/facebook/refresh-token/${integrationId}`),
    "Refresh Facebook Token",
    { maxRetries: 2 }
  );
};

export const getFacebookAccountInfo = (integrationId) => {
  return enhancedApiCall(
    () => get(`/social-auth/facebook/account-info/${integrationId}`),
    "Get Facebook Account Info"
  );
};

// Circuit breaker status functions for monitoring

export const getCircuitBreakerStatus = () => {
  return {
    state: circuitBreakerState.state,
    failures: circuitBreakerState.failures,
    lastFailureTime: circuitBreakerState.lastFailureTime,
    isHealthy: circuitBreakerState.state === "CLOSED",
  };
};

export const resetCircuitBreaker = () => {
  circuitBreakerState = {
    failures: 0,
    lastFailureTime: null,
    state: "CLOSED",
  };
  console.info("Circuit breaker manually reset");
};
