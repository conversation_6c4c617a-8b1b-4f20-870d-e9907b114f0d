import axios from "axios";
import { getCookie } from "../../utility/Utils";
import { ENV_SETTING } from "../../utility/constants";

const API_URL = ENV_SETTING.VITE_API_URL;

const axiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Interceptor de request para agregar token de autorización
axiosInstance.interceptors.request.use(
  (config) => {
    const accessToken = sessionStorage.getItem("accessToken");

    if (accessToken) {
      config.headers["Authorization"] = `Bearer ${accessToken}`;
    } else {
      const cookieToken = getCookie("accessToken");
      if (cookieToken) {
        config.headers["Authorization"] = `Bearer ${cookieToken}`;
      }
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Interceptor de response para manejar tokens revocados y errores
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      const message = error.response.data?.message;

      // Manejar tokens revocados o inválidos
      if (
        message === "Token has been revoked" ||
        message === "Invalid token" ||
        message === "No token provided"
      ) {
        // Limpiar datos de autenticación
        sessionStorage.removeItem("accessToken");
        sessionStorage.removeItem("userData");

        // Limpiar cookies
        document.cookie =
          "accessToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
        document.cookie =
          "userData=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";

        // Redirigir al login solo si no estamos ya en la página de login
        if (window.location.pathname !== "/login") {
          window.location.href = "/login";
        }
      }
    }

    if (error.response?.status === 400) {
      // Error de validación - el componente manejará el error específico
      console.log("Validation error:", error.response.data.message);
    }

    return Promise.reject(error);
  }
);

export const get = async (path) => {
  return axiosInstance.get(`${API_URL}${path}`).then((res) => res.data);
};

export const post = async (path, data) => {
  return axiosInstance.post(`${API_URL}${path}`, data).then((res) => res.data);
};

export const put = async (path, data) => {
  return axiosInstance.put(`${API_URL}${path}`, data).then((res) => res.data);
};

export const putFile = async (path, formData) => {
  let accessToken = sessionStorage.getItem("accessToken");

  if (!accessToken) {
    accessToken = getCookie("accessToken");
  }

  return fetch(`${API_URL}${path}`, {
    method: "PUT",
    body: formData,
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
};

export const del = async (path) => {
  return axiosInstance.delete(`${API_URL}${path}`).then((res) => res.data);
};

export const loginPost = async (data) =>
  axios.post(`${API_URL}/auth/login`, data);
