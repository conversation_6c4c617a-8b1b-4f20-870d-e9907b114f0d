import { loginPost, post, put } from "./request";

// Función de validación del frontend según las especificaciones del backend
const validateLoginForm = (email, password) => {
  const errors = {};
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  // Validar email
  if (!email) {
    errors.email = "Email es requerido";
  } else if (!emailRegex.test(email)) {
    errors.email = "Por favor ingrese un email válido";
  } else if (email.length > 254) {
    errors.email = "Email demasiado largo";
  }

  // Validar contraseña
  if (!password) {
    errors.password = "Contraseña es requerida";
  } else if (password.length < 6) {
    errors.password = "La contraseña debe tener al menos 6 caracteres";
  } else if (password.length > 100) {
    errors.password = "La contraseña no puede exceder 100 caracteres";
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors: errors,
  };
};

// Función para manejar errores específicos del backend
const handleLoginError = (error) => {
  const message =
    error.response?.data?.message || error.message || "Error desconocido";

  switch (message) {
    case "Email and password are required":
      return "Por favor complete todos los campos";

    case "Please provide a valid email address":
      return "Por favor ingrese un email válido";

    case "Password must be at least 6 characters long":
      return "La contraseña debe tener al menos 6 caracteres";

    case "Invalid credentials":
      return "Email o contraseña incorrectos";

    case "Email not confirmed. Please check your email and confirm your account":
      return "Por favor confirme su email antes de iniciar sesión";

    default:
      return "Error de autenticación. Intente nuevamente";
  }
};

export const login = async (email, password) => {
  try {
    // Validar datos en el frontend antes de enviar
    const validation = validateLoginForm(email, password);
    if (!validation.isValid) {
      const firstError = Object.values(validation.errors)[0];
      throw new Error(firstError);
    }

    // Realizar petición de login
    const response = await loginPost({ email, password });

    // La respuesta ahora incluye shouldUpdatePassword y refreshToken
    const { userData, accessToken, refreshToken, shouldUpdatePassword } =
      response.data;

    return {
      userData,
      accessToken,
      refreshToken,
      shouldUpdatePassword: shouldUpdatePassword || false,
    };
  } catch (error) {
    // Manejar errores específicos del backend
    const errorMessage = handleLoginError(error);
    throw new Error(errorMessage);
  }
};

export const signup = async (email, password, name) => {
  return post(`/auth/signup`, { email, password, name });
};

export const resetPassword = (email) => {
  return post(`/auth/recovery/password`, { email });
};

export const updatePassword = (password, token) => {
  return put(`/auth/change/password`, { password, token });
};

export const resendVerificationEmail = (email) => {
  return post(`/auth/send/verify-email`, { email });
};

// Función para logout individual (invalida el token actual)
export const logout = async () => {
  try {
    const response = await post("/auth/logout", {});
    return response;
  } catch (error) {
    // Incluso si falla la petición, limpiar el token local
    console.error(
      "Error en logout:",
      error.response?.data?.message || error.message
    );
    throw error;
  }
};

// Función para logout global (invalida todos los tokens del usuario)
export const logoutAll = async () => {
  try {
    const response = await post("/auth/logout-all", {});
    return response;
  } catch (error) {
    // Incluso si falla la petición, limpiar el token local
    console.error(
      "Error en logout global:",
      error.response?.data?.message || error.message
    );
    throw error;
  }
};

// Función helper para limpiar datos de autenticación del storage
export const clearAuthData = () => {
  // Limpiar sessionStorage
  sessionStorage.removeItem("accessToken");
  sessionStorage.removeItem("userData");

  // Limpiar cookies si existen
  document.cookie =
    "accessToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
  document.cookie = "userData=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
};
