# Facebook/Instagram SaaS Login Integration

## 📋 Resumen

Esta documentación describe la implementación mejorada del sistema de autenticación SaaS de Facebook/Instagram para BiitBot, que permite a los clientes integrar sus cuentas de Instagram de manera segura y eficiente.

## 🏗️ Arquitectura

### Componentes Principales

#### 1. **FacebookAuth Component** (`src/@core/components/integratoins/facebook/index.js`)
- Componente principal de la interfaz de usuario
- Maneja la visualización de cuentas conectadas
- Implementa confirmaciones modales para acciones destructivas
- Muestra estados de conexión en tiempo real

#### 2. **useFacebookAuth Hook** (`src/hooks/useFacebookAuth.js`)
- Hook personalizado que encapsula toda la lógica de negocio
- Maneja estados de carga, error y conexión
- Implementa validación de datos y manejo de errores
- Proporciona funciones para gestionar integraciones

#### 3. **Utilidades de Validación** (`src/utils/facebookAuth.js`)
- Funciones de validación y sanitización de entrada
- Construcción segura de URLs OAuth
- Manejo de errores específicos de Facebook/Instagram

#### 4. **Servicios API Mejorados** (`src/services/socialAuth.js`)
- Implementa circuit breaker pattern
- Retry logic con backoff exponencial
- Manejo robusto de errores HTTP

## 🔧 Configuración

### Variables de Entorno

```bash
# Facebook App Configuration
VITE_FACEBOOK_CLIENT_ID=your_facebook_app_id

# Environment-specific settings
NODE_ENV=development|production
```

### Configuración de Facebook App

1. **Crear Facebook App**:
   - Ir a [Facebook Developers](https://developers.facebook.com/)
   - Crear nueva app de tipo "Business"
   - Agregar producto "Instagram Basic Display"

2. **Configurar OAuth**:
   - **Valid OAuth Redirect URIs**:
     - Desarrollo: `https://caiman-legal-treefrog.ngrok-free.app/social-auth/facebook/callback`
     - Producción: `https://app.biitbot.com/social-auth/facebook/callback`

3. **Permisos Requeridos**:
   - `instagram_business_basic`
   - `instagram_business_manage_messages`
   - `pages_show_list`
   - `instagram_manage_messages`
   - `instagram_basic`
   - `human_agent`

## 🚀 Uso

### Integración Básica

```jsx
import FacebookAuth from '@core/components/integratoins/facebook';

function MyComponent() {
  const botRequestId = "your-bot-request-id";
  
  return (
    <FacebookAuth botRequestId={botRequestId} />
  );
}
```

### Uso del Hook Personalizado

```jsx
import { useFacebookAuth } from '@hooks/useFacebookAuth';

function CustomComponent({ botRequestId }) {
  const {
    linkedAccount,
    isLoading,
    error,
    errorType,
    handleFacebookLogin,
    handleUnlinkIntegration,
    refreshIntegrations
  } = useFacebookAuth(botRequestId);

  // Tu lógica personalizada aquí
}
```

## 🔒 Seguridad

### Validación de Entrada

Todas las entradas son validadas usando las utilidades en `facebookAuth.js`:

```javascript
import { validateBotRequestId, sanitizeUrlParam } from '@utils/facebookAuth';

// Validar ID de bot
const validation = validateBotRequestId(botRequestId);
if (!validation.isValid) {
  console.error('Invalid bot ID:', validation.errors);
}

// Sanitizar parámetros de URL
const cleanParam = sanitizeUrlParam(userInput);
```

### Construcción Segura de URLs

```javascript
import { buildFacebookOAuthUrl } from '@utils/facebookAuth';

const urlResult = buildFacebookOAuthUrl(botRequestId);
if (urlResult.success) {
  window.open(urlResult.url, '_self');
} else {
  console.error('URL construction failed:', urlResult.error);
}
```

## 🔄 Manejo de Errores

### Tipos de Error

El sistema identifica automáticamente diferentes tipos de errores:

- **`network`**: Problemas de conectividad
- **`authentication`**: Errores de autenticación OAuth
- **`configuration`**: Problemas de configuración
- **`timeout`**: Timeouts de solicitud
- **`rateLimit`**: Límites de tasa excedidos
- **`general`**: Errores generales

### Uso del ErrorDisplay

```jsx
import ErrorDisplay from '@core/components/common/ErrorBoundary';

<ErrorDisplay
  error={error}
  errorType={errorType}
  onRetry={handleRetry}
  onDismiss={handleDismiss}
  showRetry={true}
/>
```

## 📊 Monitoreo

### Circuit Breaker Status

```javascript
import { getCircuitBreakerStatus, resetCircuitBreaker } from '@services/socialAuth';

// Verificar estado del circuit breaker
const status = getCircuitBreakerStatus();
console.log('Circuit breaker status:', status);

// Reset manual si es necesario
if (!status.isHealthy) {
  resetCircuitBreaker();
}
```

### Métricas de Rendimiento

El sistema incluye logging automático para:
- Intentos de conexión
- Errores de API
- Tiempos de respuesta
- Estados del circuit breaker

## 🧪 Testing

### Tests Unitarios

```bash
# Ejecutar tests de utilidades
npm test src/utils/facebookAuth.test.js

# Ejecutar tests del hook
npm test src/hooks/useFacebookAuth.test.js

# Ejecutar tests del componente
npm test src/@core/components/integratoins/facebook/index.test.js
```

### Tests de Integración

```bash
# Test completo del flujo de autenticación
npm test src/__tests__/facebook-auth-integration.test.js
```

## 🔧 Troubleshooting

### Problemas Comunes

1. **Error de configuración**:
   ```
   Error: VITE_FACEBOOK_CLIENT_ID environment variable is required
   ```
   **Solución**: Verificar que la variable de entorno esté configurada

2. **Error de callback URL**:
   ```
   Error: Invalid OAuth redirect URI
   ```
   **Solución**: Verificar que la URL de callback esté configurada en Facebook App

3. **Error de permisos**:
   ```
   Error: Insufficient permissions
   ```
   **Solución**: Verificar que todos los permisos requeridos estén otorgados

### Logs de Debug

En desarrollo, habilitar logs detallados:

```javascript
// En el navegador
localStorage.setItem('debug', 'facebook-auth:*');
```

## 📈 Mejoras Futuras

### Funcionalidades Planificadas

- [ ] Renovación automática de tokens
- [ ] Sincronización en tiempo real de mensajes
- [ ] Dashboard de métricas de Instagram
- [ ] Soporte para múltiples páginas de Facebook
- [ ] Integración con Instagram Stories

### Optimizaciones

- [ ] Caching de respuestas de API
- [ ] Lazy loading de componentes
- [ ] Optimización de bundle size
- [ ] Service Worker para offline support

## 📞 Soporte

Para problemas técnicos o preguntas sobre la implementación:

1. Revisar esta documentación
2. Verificar logs en la consola del navegador
3. Consultar el estado del circuit breaker
4. Contactar al equipo de desarrollo

---

**Última actualización**: Junio 2025  
**Versión**: 2.0.0  
**Autor**: BiitBot Development Team
