# Facebook/Instagram Integration - Development Guide

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- Facebook Developer Account
- ngrok (for local development)

### Setup

1. **Clone and Install**:
   ```bash
   git clone <repository>
   cd crm.bot.biitbot
   npm install
   ```

2. **Environment Configuration**:
   ```bash
   cp .env.example .env
   # Edit .env with your Facebook App ID
   VITE_FACEBOOK_CLIENT_ID=your_facebook_app_id
   ```

3. **Start Development Server**:
   ```bash
   npm run dev
   ```

4. **Setup ngrok** (for OAuth callback):
   ```bash
   ngrok http 3000
   # Update callback URL in Facebook App settings
   ```

## 🏗️ Development Workflow

### Branch Strategy

```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and commit
git add .
git commit -m "feat: your feature description"

# Push and create PR
git push origin feature/your-feature-name
```

### Code Standards

#### React Components

```jsx
// ✅ Good - Functional component with hooks
import React from 'react';
import { useTranslation } from 'react-i18next';

const MyComponent = ({ prop1, prop2 }) => {
  const { t } = useTranslation();
  
  return (
    <div>
      {/* Component content */}
    </div>
  );
};

export default MyComponent;
```

#### Custom Hooks

```jsx
// ✅ Good - Custom hook pattern
export const useMyFeature = (param) => {
  const [state, setState] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Hook logic here

  return {
    state,
    loading,
    error,
    actions: {
      doSomething,
      reset
    }
  };
};
```

#### Error Handling

```jsx
// ✅ Good - Comprehensive error handling
try {
  const result = await apiCall();
  return result;
} catch (error) {
  const errorType = determineErrorType(error);
  setError({ message: error.message, type: errorType });
  
  // Log for debugging
  console.error('Operation failed:', error);
  
  // User-friendly notification
  toast.error(t('errors.operationFailed'));
}
```

### File Structure

```
src/
├── @core/
│   └── components/
│       ├── common/           # Reusable components
│       │   ├── ConfirmationModal.js
│       │   ├── ConnectionStatus.js
│       │   └── ErrorBoundary.js
│       └── integratoins/
│           └── facebook/
│               └── index.js  # Main Facebook component
├── hooks/
│   └── useFacebookAuth.js    # Custom hook
├── services/
│   └── socialAuth.js         # API services
├── utils/
│   └── facebookAuth.js       # Utilities
└── assets/
    └── data/
        └── locales/          # Translations
            ├── en.json
            └── es.json
```

## 🧪 Testing

### Unit Tests

```bash
# Run specific test file
npm test src/utils/facebookAuth.test.js

# Run tests in watch mode
npm test -- --watch

# Run tests with coverage
npm test -- --coverage
```

### Test Structure

```javascript
// Example test file
import { validateBotRequestId } from '../facebookAuth';

describe('validateBotRequestId', () => {
  it('should validate correct bot request ID', () => {
    const result = validateBotRequestId('valid-bot-id-123');
    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  it('should reject invalid bot request ID', () => {
    const result = validateBotRequestId('');
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain('botRequestId is required');
  });
});
```

### Integration Tests

```javascript
// Example integration test
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import FacebookAuth from '../index';

describe('FacebookAuth Integration', () => {
  it('should handle complete auth flow', async () => {
    render(<FacebookAuth botRequestId="test-bot-id" />);
    
    const connectButton = screen.getByText('Connect with Instagram');
    fireEvent.click(connectButton);
    
    await waitFor(() => {
      expect(window.open).toHaveBeenCalledWith(
        expect.stringContaining('facebook.com'),
        '_self'
      );
    });
  });
});
```

## 🔧 Debugging

### Development Tools

1. **React Developer Tools**:
   - Install browser extension
   - Inspect component state and props

2. **Redux DevTools** (if using Redux):
   - Monitor state changes
   - Time-travel debugging

3. **Network Tab**:
   - Monitor API calls
   - Check request/response headers

### Common Debug Scenarios

#### OAuth Flow Issues

```javascript
// Debug OAuth URL construction
const urlResult = buildFacebookOAuthUrl(botRequestId);
console.log('OAuth URL:', urlResult);

// Check if URL is valid
if (!urlResult.success) {
  console.error('URL construction failed:', urlResult.error);
}
```

#### API Call Failures

```javascript
// Enable detailed API logging
localStorage.setItem('debug', 'api:*');

// Check circuit breaker status
const cbStatus = getCircuitBreakerStatus();
console.log('Circuit breaker:', cbStatus);
```

#### Component State Issues

```javascript
// Debug hook state
const hookState = useFacebookAuth(botRequestId);
console.log('Hook state:', hookState);

// Use React DevTools to inspect component tree
```

## 📦 Build and Deploy

### Development Build

```bash
# Start development server
npm run dev

# Build for development
npm run build:dev
```

### Production Build

```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

### Environment-Specific Configs

```javascript
// vite.config.js
export default defineConfig({
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
  },
  // ... other config
});
```

## 🔍 Code Review Checklist

### Before Submitting PR

- [ ] All tests pass
- [ ] Code follows style guidelines
- [ ] No console.log statements in production code
- [ ] Error handling is comprehensive
- [ ] Components are properly typed
- [ ] Translations are updated
- [ ] Documentation is updated

### Review Criteria

- [ ] **Functionality**: Does it work as expected?
- [ ] **Security**: Are inputs validated and sanitized?
- [ ] **Performance**: Are there any performance issues?
- [ ] **Accessibility**: Is the UI accessible?
- [ ] **Maintainability**: Is the code easy to understand and modify?

## 🚨 Common Pitfalls

### 1. OAuth Callback URL Mismatch

```javascript
// ❌ Wrong - Hardcoded URL
const callbackUrl = 'https://localhost:3000/callback';

// ✅ Right - Dynamic URL based on environment
const callbackUrl = FACEBOOK_CONFIG.CALLBACK_URL;
```

### 2. Missing Error Handling

```javascript
// ❌ Wrong - No error handling
const data = await apiCall();
setData(data);

// ✅ Right - Comprehensive error handling
try {
  const data = await apiCall();
  setData(data);
  setError(null);
} catch (error) {
  setError(error.message);
  console.error('API call failed:', error);
}
```

### 3. State Management Issues

```javascript
// ❌ Wrong - Direct state mutation
state.items.push(newItem);

// ✅ Right - Immutable updates
setState(prev => [...prev, newItem]);
```

## 📚 Resources

### Documentation

- [Facebook Graph API](https://developers.facebook.com/docs/graph-api/)
- [Instagram Basic Display API](https://developers.facebook.com/docs/instagram-basic-display-api/)
- [React Hooks](https://reactjs.org/docs/hooks-intro.html)

### Tools

- [Facebook Graph API Explorer](https://developers.facebook.com/tools/explorer/)
- [ngrok](https://ngrok.com/) - Secure tunneling
- [Postman](https://www.postman.com/) - API testing

---

**Happy Coding! 🚀**
