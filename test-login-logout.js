// Script de prueba para validar las nuevas funcionalidades de login y logout
// Este archivo es solo para testing y debe ser eliminado después de las pruebas

import { authRequest } from './src/services';

// Función para probar el flujo completo de login
const testLoginFlow = async () => {
  console.log('🧪 Iniciando pruebas de login...');
  
  try {
    // 1. Probar validaciones del frontend
    console.log('1. Probando validaciones del frontend...');
    
    // Email inválido
    try {
      await authRequest.login('email-invalido', 'password123');
      console.log('❌ ERROR: Debería haber fallado con email inválido');
    } catch (error) {
      console.log('✅ Validación de email funcionó:', error.message);
    }
    
    // Contraseña muy corta
    try {
      await authRequest.login('<EMAIL>', '123');
      console.log('❌ ERROR: Debería haber fallado con contraseña corta');
    } catch (error) {
      console.log('✅ Validación de contraseña funcionó:', error.message);
    }
    
    // 2. Probar login exitoso (requiere credenciales válidas)
    console.log('2. Para probar login exitoso, usar credenciales válidas en el navegador');
    
  } catch (error) {
    console.error('❌ Error en test de login:', error);
  }
};

// Función para probar el flujo de logout
const testLogoutFlow = async () => {
  console.log('🧪 Iniciando pruebas de logout...');
  
  try {
    // 1. Verificar que las funciones existen
    console.log('1. Verificando funciones de logout...');
    
    if (typeof authRequest.logout === 'function') {
      console.log('✅ Función logout existe');
    } else {
      console.log('❌ Función logout no existe');
    }
    
    if (typeof authRequest.logoutAll === 'function') {
      console.log('✅ Función logoutAll existe');
    } else {
      console.log('❌ Función logoutAll no existe');
    }
    
    if (typeof authRequest.clearAuthData === 'function') {
      console.log('✅ Función clearAuthData existe');
    } else {
      console.log('❌ Función clearAuthData no existe');
    }
    
    // 2. Probar limpieza de datos (sin hacer logout real)
    console.log('2. Probando limpieza de datos...');
    
    // Simular datos en storage
    sessionStorage.setItem('test-token', 'test-value');
    authRequest.clearAuthData();
    
    const tokenAfterClear = sessionStorage.getItem('accessToken');
    if (!tokenAfterClear) {
      console.log('✅ Limpieza de sessionStorage funcionó');
    } else {
      console.log('❌ Limpieza de sessionStorage falló');
    }
    
  } catch (error) {
    console.error('❌ Error en test de logout:', error);
  }
};

// Función para probar manejo de errores
const testErrorHandling = () => {
  console.log('🧪 Iniciando pruebas de manejo de errores...');
  
  // Simular diferentes tipos de errores
  const testErrors = [
    { message: 'Email and password are required', expected: 'Por favor complete todos los campos' },
    { message: 'Please provide a valid email address', expected: 'Por favor ingrese un email válido' },
    { message: 'Password must be at least 6 characters long', expected: 'La contraseña debe tener al menos 6 caracteres' },
    { message: 'Invalid credentials', expected: 'Email o contraseña incorrectos' },
    { message: 'Email not confirmed. Please check your email and confirm your account', expected: 'Por favor confirme su email antes de iniciar sesión' },
    { message: 'Unknown error', expected: 'Error de autenticación. Intente nuevamente' }
  ];
  
  testErrors.forEach((testCase, index) => {
    console.log(`${index + 1}. Probando error: "${testCase.message}"`);
    
    // Aquí normalmente probaríamos la función handleLoginError
    // pero está dentro del módulo, así que solo verificamos la lógica
    console.log(`   Esperado: "${testCase.expected}"`);
  });
  
  console.log('✅ Todos los casos de error están definidos');
};

// Ejecutar todas las pruebas
const runAllTests = async () => {
  console.log('🚀 Iniciando todas las pruebas...\n');
  
  await testLoginFlow();
  console.log('\n');
  
  await testLogoutFlow();
  console.log('\n');
  
  testErrorHandling();
  console.log('\n');
  
  console.log('🎉 Pruebas completadas. Revisar logs para resultados.');
  console.log('\n📝 Notas:');
  console.log('- Para probar login completo, usar el navegador con credenciales válidas');
  console.log('- Para probar logout completo, hacer login primero y luego usar el botón de logout');
  console.log('- Verificar que el interceptor de Axios maneja tokens revocados correctamente');
};

// Exportar funciones para uso en consola del navegador
if (typeof window !== 'undefined') {
  window.testLoginLogout = {
    runAllTests,
    testLoginFlow,
    testLogoutFlow,
    testErrorHandling
  };
  
  console.log('🔧 Funciones de prueba disponibles en window.testLoginLogout');
  console.log('   - runAllTests(): Ejecutar todas las pruebas');
  console.log('   - testLoginFlow(): Probar solo login');
  console.log('   - testLogoutFlow(): Probar solo logout');
  console.log('   - testErrorHandling(): Probar manejo de errores');
}

export { runAllTests, testLoginFlow, testLogoutFlow, testErrorHandling };
