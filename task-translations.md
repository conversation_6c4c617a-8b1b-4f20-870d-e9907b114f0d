# Task: Ag<PERSON><PERSON>rad<PERSON> para Providers "Coming Soon"

## Objetivo
Agregar las traducciones faltantes para los providers en la sección "Coming soon" en ambos idiomas (español e inglés).

## Cambios Realizados

### ✅ Archivo: `src/assets/data/locales/es.json`
Agregadas las siguientes traducciones en la sección `bot.listOfBots`:

```json
"comingSoonProviders": "Próximamente proveedores de chat",
"providers": {
  "telegram": "Telegram",
  "instagram": "Instagram", 
  "facebookMessenger": "Facebook Messenger",
  "slack": "Slack",
  "discord": "Discord"
}
```

### ✅ Archivo: `src/assets/data/locales/en.json`
Agregadas las siguientes traducciones en la sección `bot.listOfBots`:

```json
"comingSoonProviders": "Coming soon chat providers",
"providers": {
  "telegram": "Telegram",
  "instagram": "Instagram",
  "facebookMessenger": "Facebook Messenger", 
  "slack": "Slack",
  "discord": "Discord"
}
```

### ✅ Archivo: `src/views/pages/bots/BotList.js`
Actualizadas las referencias en el componente `ProviderDropdownItems`:

- **Título de sección**: `{t("bot.listOfBots.comingSoonProviders")}`
- **Providers individuales**: 
  - `{t("bot.listOfBots.providers.telegram")}`
  - `{t("bot.listOfBots.providers.instagram")}`
  - `{t("bot.listOfBots.providers.facebookMessenger")}`
  - `{t("bot.listOfBots.providers.slack")}`
  - `{t("bot.listOfBots.providers.discord")}`

## Beneficios

### 🌐 Internacionalización Completa
- Soporte completo para español e inglés
- Consistencia en toda la aplicación
- Fácil mantenimiento de traducciones

### 🔧 Mantenibilidad
- Traducciones centralizadas en archivos JSON
- Fácil agregar nuevos idiomas en el futuro
- Cambios de texto sin modificar código

### 🎯 Experiencia de Usuario
- Interfaz completamente traducida
- Consistencia visual y textual
- Profesionalismo en ambos idiomas

## Estado Final
- ✅ Todas las traducciones agregadas
- ✅ Componente actualizado para usar traducciones
- ✅ Soporte completo ES/EN
- ✅ Listo para commit
