# Facebook/Instagram Backend Testing Examples

## 🧪 Testing Strategy

### Test Structure
```
tests/
├── unit/
│   ├── services/
│   │   ├── facebook-auth.test.js
│   │   ├── instagram-api.test.js
│   │   └── token-management.test.js
│   ├── controllers/
│   │   ├── social-auth.test.js
│   │   └── webhooks.test.js
│   └── utils/
│       ├── validation.test.js
│       └── security.test.js
├── integration/
│   ├── oauth-flow.test.js
│   ├── webhook-processing.test.js
│   └── database-operations.test.js
└── e2e/
    ├── complete-auth-flow.test.js
    └── message-processing.test.js
```

## 🔧 Unit Tests

### Facebook Auth Service Tests
```javascript
// tests/unit/services/facebook-auth.test.js
const { exchangeCodeForToken, getFacebookPagesAndInstagramAccounts } = require('../../../services/facebook-auth');
const nock = require('nock');

describe('Facebook Auth Service', () => {
  beforeEach(() => {
    nock.cleanAll();
  });

  describe('exchangeCodeForToken', () => {
    it('should successfully exchange code for access token', async () => {
      const mockCode = 'test_authorization_code';
      const mockResponse = {
        access_token: 'test_access_token',
        token_type: 'bearer',
        expires_in: 3600
      };

      nock('https://graph.facebook.com')
        .post('/v23.0/oauth/access_token')
        .reply(200, mockResponse);

      const result = await exchangeCodeForToken(mockCode);

      expect(result).toEqual(mockResponse);
      expect(result.access_token).toBe('test_access_token');
    });

    it('should handle token exchange errors', async () => {
      const mockCode = 'invalid_code';

      nock('https://graph.facebook.com')
        .post('/v23.0/oauth/access_token')
        .reply(400, {
          error: {
            message: 'Invalid authorization code',
            type: 'OAuthException',
            code: 100
          }
        });

      await expect(exchangeCodeForToken(mockCode)).rejects.toThrow('Token exchange failed');
    });
  });

  describe('getFacebookPagesAndInstagramAccounts', () => {
    it('should fetch pages and Instagram accounts', async () => {
      const mockAccessToken = 'test_token';
      const mockPagesResponse = {
        data: [
          {
            id: 'page_123',
            name: 'Test Page',
            access_token: 'page_token_123',
            tasks: ['MANAGE', 'CREATE_CONTENT']
          }
        ]
      };

      const mockInstagramResponse = {
        instagram_business_account: {
          id: 'ig_123'
        }
      };

      const mockInstagramDetails = {
        id: 'ig_123',
        name: 'Test Instagram',
        username: 'test_instagram',
        profile_picture_url: 'https://example.com/pic.jpg'
      };

      nock('https://graph.facebook.com')
        .get('/v23.0/me/accounts')
        .query({ access_token: mockAccessToken })
        .reply(200, mockPagesResponse);

      nock('https://graph.facebook.com')
        .get('/v23.0/page_123')
        .query({ fields: 'instagram_business_account', access_token: 'page_token_123' })
        .reply(200, mockInstagramResponse);

      nock('https://graph.facebook.com')
        .get('/v23.0/ig_123')
        .query({ fields: 'name,username,profile_picture_url', access_token: 'page_token_123' })
        .reply(200, mockInstagramDetails);

      const result = await getFacebookPagesAndInstagramAccounts(mockAccessToken);

      expect(result).toHaveLength(2); // 1 Facebook page + 1 Instagram account
      expect(result[0].provider).toBe('facebook');
      expect(result[1].provider).toBe('instagram');
      expect(result[1].accountName).toBe('Test Instagram');
    });
  });
});
```

### Controller Tests
```javascript
// tests/unit/controllers/social-auth.test.js
const request = require('supertest');
const app = require('../../../app');
const db = require('../../../database');

jest.mock('../../../database');
jest.mock('../../../services/facebook-auth');

describe('Social Auth Controller', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /social-auth/botCompany', () => {
    it('should return user integrations', async () => {
      const mockUser = { companyId: 'company_123' };
      const mockAccounts = [
        {
          id: 'account_123',
          account_name: 'Test Instagram',
          account_id: '@test_instagram',
          type: 'chat',
          provider: 'instagram',
          is_active: true,
          created_at: '2024-01-01T00:00:00Z',
          bot_request_id: 'bot_123',
          selected_bot_company_id: 'selection_123'
        }
      ];

      db.query.mockResolvedValue({ rows: mockAccounts });

      const response = await request(app)
        .get('/social-auth/botCompany')
        .set('Authorization', 'Bearer valid_token')
        .expect(200);

      expect(response.body.socialAuthAccount).toHaveLength(1);
      expect(response.body.socialAuthAccount[0].accountName).toBe('Test Instagram');
      expect(response.body.socialAuthAccount[0].selectedBotCompany).toBeTruthy();
    });

    it('should require authentication', async () => {
      await request(app)
        .get('/social-auth/botCompany')
        .expect(401);
    });
  });

  describe('PUT /social-auth/integration/select', () => {
    it('should update selected integration', async () => {
      const mockUser = { companyId: 'company_123' };
      const requestBody = {
        socialAuthId: 'account_123',
        botRequestId: 'bot_456'
      };

      db.query
        .mockResolvedValueOnce({ rows: [{ id: 'account_123' }] }) // Account exists
        .mockResolvedValueOnce({ rows: [] }) // Delete existing selection
        .mockResolvedValueOnce({ rows: [] }); // Insert new selection

      const response = await request(app)
        .put('/social-auth/integration/select')
        .set('Authorization', 'Bearer valid_token')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(db.query).toHaveBeenCalledTimes(3);
    });

    it('should validate required fields', async () => {
      await request(app)
        .put('/social-auth/integration/select')
        .set('Authorization', 'Bearer valid_token')
        .send({})
        .expect(400);
    });
  });
});
```

## 🔗 Integration Tests

### OAuth Flow Integration Test
```javascript
// tests/integration/oauth-flow.test.js
const request = require('supertest');
const app = require('../../app');
const db = require('../../database');
const nock = require('nock');

describe('OAuth Flow Integration', () => {
  beforeAll(async () => {
    // Setup test database
    await db.query('BEGIN');
  });

  afterAll(async () => {
    // Cleanup test database
    await db.query('ROLLBACK');
  });

  it('should complete full OAuth flow', async () => {
    // Step 1: Generate auth URL
    const authUrlResponse = await request(app)
      .get('/social-auth/facebook/test_bot_123/auth-url')
      .set('Authorization', 'Bearer valid_token')
      .expect(200);

    expect(authUrlResponse.body.authUrl).toContain('facebook.com');
    expect(authUrlResponse.body.authUrl).toContain('test_bot_123');

    // Step 2: Mock Facebook token exchange
    nock('https://graph.facebook.com')
      .post('/v23.0/oauth/access_token')
      .reply(200, {
        access_token: 'test_access_token',
        token_type: 'bearer',
        expires_in: 3600
      });

    // Mock Facebook pages API
    nock('https://graph.facebook.com')
      .get('/v23.0/me/accounts')
      .reply(200, {
        data: [
          {
            id: 'page_123',
            name: 'Test Page',
            access_token: 'page_token_123'
          }
        ]
      });

    // Step 3: Handle OAuth callback
    const callbackResponse = await request(app)
      .get('/social-auth/facebook/callback')
      .query({
        code: 'test_authorization_code',
        state: 'test_bot_123:company_123'
      })
      .expect(302); // Redirect

    expect(callbackResponse.headers.location).toContain('success=true');

    // Step 4: Verify account was stored
    const integrations = await request(app)
      .get('/social-auth/botCompany')
      .set('Authorization', 'Bearer valid_token')
      .expect(200);

    expect(integrations.body.socialAuthAccount).toHaveLength(1);
    expect(integrations.body.socialAuthAccount[0].accountName).toBe('Test Page');
  });
});
```

### Webhook Processing Test
```javascript
// tests/integration/webhook-processing.test.js
const request = require('supertest');
const app = require('../../app');
const db = require('../../database');

describe('Instagram Webhook Processing', () => {
  beforeEach(async () => {
    // Setup test account in database
    await db.query(`
      INSERT INTO social_auth_accounts (id, company_id, provider, provider_account_id, account_name, access_token)
      VALUES ('test_account_id', 'test_company_id', 'instagram', 'ig_123', 'Test Instagram', 'test_token')
    `);
  });

  afterEach(async () => {
    // Cleanup test data
    await db.query('DELETE FROM social_auth_accounts WHERE id = $1', ['test_account_id']);
  });

  it('should process Instagram message webhook', async () => {
    const webhookPayload = {
      object: 'instagram',
      entry: [
        {
          messaging: [
            {
              sender: { id: 'user_123' },
              recipient: { id: 'ig_123' },
              timestamp: Date.now(),
              message: {
                mid: 'message_123',
                text: 'Hello from Instagram!'
              }
            }
          ]
        }
      ]
    };

    const response = await request(app)
      .post('/webhooks/instagram')
      .set('X-Hub-Signature-256', generateWebhookSignature(webhookPayload))
      .send(webhookPayload)
      .expect(200);

    expect(response.text).toBe('EVENT_RECEIVED');

    // Verify message was stored
    const messages = await db.query(
      'SELECT * FROM messages WHERE external_message_id = $1',
      ['message_123']
    );

    expect(messages.rows).toHaveLength(1);
    expect(messages.rows[0].content).toBe('Hello from Instagram!');
  });

  it('should verify webhook signature', async () => {
    const webhookPayload = { test: 'data' };

    await request(app)
      .post('/webhooks/instagram')
      .set('X-Hub-Signature-256', 'invalid_signature')
      .send(webhookPayload)
      .expect(401);
  });
});
```

## 🎯 End-to-End Tests

### Complete Authentication Flow
```javascript
// tests/e2e/complete-auth-flow.test.js
const puppeteer = require('puppeteer');
const request = require('supertest');
const app = require('../../app');

describe('Complete Authentication Flow E2E', () => {
  let browser, page;

  beforeAll(async () => {
    browser = await puppeteer.launch({ headless: true });
    page = await browser.newPage();
  });

  afterAll(async () => {
    await browser.close();
  });

  it('should complete full user authentication flow', async () => {
    // Step 1: Get auth URL from backend
    const authResponse = await request(app)
      .get('/social-auth/facebook/test_bot_e2e/auth-url')
      .set('Authorization', 'Bearer valid_token')
      .expect(200);

    const authUrl = authResponse.body.authUrl;

    // Step 2: Navigate to Facebook OAuth (in test environment, this would be mocked)
    await page.goto(authUrl);

    // In a real E2E test, you would:
    // - Fill in Facebook login credentials
    // - Grant permissions
    // - Handle the redirect back to your callback

    // For testing purposes, we'll simulate the callback directly
    const callbackUrl = `${process.env.BASE_URL}/social-auth/facebook/callback?code=test_code&state=test_bot_e2e:test_company`;

    // Step 3: Simulate callback processing
    const callbackResponse = await request(app)
      .get('/social-auth/facebook/callback')
      .query({
        code: 'test_authorization_code',
        state: 'test_bot_e2e:test_company'
      });

    // Step 4: Verify integration was created
    const integrations = await request(app)
      .get('/social-auth/botCompany')
      .set('Authorization', 'Bearer valid_token')
      .expect(200);

    expect(integrations.body.socialAuthAccount.length).toBeGreaterThan(0);
  });
});
```

## 🔧 Test Utilities

### Mock Data Factory
```javascript
// tests/utils/mock-factory.js
class MockFactory {
  static createFacebookPage(overrides = {}) {
    return {
      id: 'page_123',
      name: 'Test Facebook Page',
      access_token: 'page_access_token',
      tasks: ['MANAGE', 'CREATE_CONTENT'],
      ...overrides
    };
  }

  static createInstagramAccount(overrides = {}) {
    return {
      id: 'ig_123',
      name: 'Test Instagram Account',
      username: 'test_instagram',
      profile_picture_url: 'https://example.com/pic.jpg',
      ...overrides
    };
  }

  static createWebhookMessage(overrides = {}) {
    return {
      object: 'instagram',
      entry: [
        {
          messaging: [
            {
              sender: { id: 'user_123' },
              recipient: { id: 'ig_123' },
              timestamp: Date.now(),
              message: {
                mid: 'message_123',
                text: 'Test message',
                ...overrides.message
              },
              ...overrides.messaging
            }
          ]
        }
      ]
    };
  }

  static createSocialAuthAccount(overrides = {}) {
    return {
      id: 'account_123',
      company_id: 'company_123',
      provider: 'instagram',
      provider_account_id: 'ig_123',
      account_name: 'Test Instagram',
      account_id: '@test_instagram',
      type: 'chat',
      access_token: 'test_token',
      is_active: true,
      created_at: new Date().toISOString(),
      ...overrides
    };
  }
}

module.exports = MockFactory;
```

### Test Database Setup
```javascript
// tests/utils/test-db.js
const { Pool } = require('pg');

const testDb = new Pool({
  connectionString: process.env.TEST_DATABASE_URL,
  ssl: false
});

async function setupTestDatabase() {
  await testDb.query('BEGIN');
  
  // Create test tables if they don't exist
  await testDb.query(`
    CREATE TABLE IF NOT EXISTS test_social_auth_accounts (
      LIKE social_auth_accounts INCLUDING ALL
    )
  `);
  
  await testDb.query(`
    CREATE TABLE IF NOT EXISTS test_selected_bot_companies (
      LIKE selected_bot_companies INCLUDING ALL
    )
  `);
}

async function cleanupTestDatabase() {
  await testDb.query('ROLLBACK');
}

module.exports = {
  testDb,
  setupTestDatabase,
  cleanupTestDatabase
};
```

## 📊 Test Configuration

### Jest Configuration
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  testMatch: [
    '<rootDir>/tests/**/*.test.js'
  ],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/**/*.test.js',
    '!src/migrations/**'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  testTimeout: 30000
};
```

### Test Setup
```javascript
// tests/setup.js
const { setupTestDatabase, cleanupTestDatabase } = require('./utils/test-db');

beforeAll(async () => {
  await setupTestDatabase();
});

afterAll(async () => {
  await cleanupTestDatabase();
});

// Mock external services
jest.mock('../services/socket-io', () => ({
  emit: jest.fn(),
  to: jest.fn().mockReturnThis()
}));
```

This testing suite provides comprehensive coverage for the Facebook/Instagram backend integration.
