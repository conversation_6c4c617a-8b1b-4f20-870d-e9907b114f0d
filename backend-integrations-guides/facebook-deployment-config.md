# Facebook/Instagram Backend Deployment Configuration

## 🔧 Environment Configuration

### .env File Template
```bash
# Facebook App Configuration
FACEBOOK_CLIENT_ID=2107066629685377
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret_here
FACEBOOK_API_VERSION=v23.0

# Environment-specific Callback URLs
NODE_ENV=development
FACEBOOK_CALLBACK_URL_DEV=https://caiman-legal-treefrog.ngrok-free.app/social-auth/facebook/callback
FACEBOOK_CALLBACK_URL_PROD=https://app.biitbot.com/social-auth/facebook/callback

# Frontend URLs
FRONTEND_URL_DEV=https://caiman-legal-treefrog.ngrok-free.app
FRONTEND_URL_PROD=https://app.biitbot.com

# Instagram Webhook Configuration
INSTAGRAM_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token_here
INSTAGRAM_WEBHOOK_SECRET=your_webhook_secret_here

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/biitbot
DATABASE_SSL=true

# Redis Configuration (for rate limiting and caching)
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h

# Logging
LOG_LEVEL=info
LOG_FILE=logs/facebook-integration.log
```

## 🗄️ Database Migration Scripts

### Initial Migration
```sql
-- Migration: 001_create_social_auth_tables.sql

-- Create social_auth_accounts table
CREATE TABLE IF NOT EXISTS social_auth_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL,
    provider VARCHAR(50) NOT NULL CHECK (provider IN ('facebook', 'instagram', 'google', 'whatsapp')),
    provider_account_id VARCHAR(255) NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    account_id VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('chat', 'page', 'group')),
    access_token TEXT NOT NULL,
    refresh_token TEXT,
    token_expires_at TIMESTAMP,
    scopes TEXT[],
    account_data JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    CONSTRAINT unique_provider_account UNIQUE(provider, provider_account_id, company_id)
);

-- Create selected_bot_companies table
CREATE TABLE IF NOT EXISTS selected_bot_companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    social_auth_account_id UUID NOT NULL REFERENCES social_auth_accounts(id) ON DELETE CASCADE,
    bot_request_id VARCHAR(255) NOT NULL,
    company_id UUID NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    CONSTRAINT unique_bot_selection UNIQUE(bot_request_id, company_id)
);

-- Create indexes for performance
CREATE INDEX idx_social_auth_company_provider ON social_auth_accounts(company_id, provider);
CREATE INDEX idx_social_auth_provider_account ON social_auth_accounts(provider, provider_account_id);
CREATE INDEX idx_selected_bot_companies_bot_id ON selected_bot_companies(bot_request_id);
CREATE INDEX idx_social_auth_active ON social_auth_accounts(is_active) WHERE is_active = true;

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_social_auth_accounts_updated_at 
    BEFORE UPDATE ON social_auth_accounts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_selected_bot_companies_updated_at 
    BEFORE UPDATE ON selected_bot_companies 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### Messages Table Extension
```sql
-- Migration: 002_extend_messages_for_instagram.sql

-- Add Instagram-specific columns to existing messages table
ALTER TABLE messages ADD COLUMN IF NOT EXISTS platform VARCHAR(50) DEFAULT 'whatsapp';
ALTER TABLE messages ADD COLUMN IF NOT EXISTS social_auth_account_id UUID REFERENCES social_auth_accounts(id);
ALTER TABLE messages ADD COLUMN IF NOT EXISTS external_message_id VARCHAR(255);
ALTER TABLE messages ADD COLUMN IF NOT EXISTS sender_platform_id VARCHAR(255);
ALTER TABLE messages ADD COLUMN IF NOT EXISTS recipient_platform_id VARCHAR(255);

-- Create indexes for Instagram messages
CREATE INDEX IF NOT EXISTS idx_messages_platform ON messages(platform);
CREATE INDEX IF NOT EXISTS idx_messages_social_auth ON messages(social_auth_account_id);
CREATE INDEX IF NOT EXISTS idx_messages_external_id ON messages(external_message_id);
```

## 🚀 Docker Configuration

### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Create logs directory
RUN mkdir -p logs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Start application
CMD ["npm", "start"]
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - FACEBOOK_CLIENT_ID=${FACEBOOK_CLIENT_ID}
      - FACEBOOK_CLIENT_SECRET=${FACEBOOK_CLIENT_SECRET}
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=biitbot
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

## 🔧 Nginx Configuration

### nginx.conf
```nginx
upstream biitbot_api {
    server localhost:3000;
}

server {
    listen 80;
    server_name api.biitbot.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.biitbot.com;

    ssl_certificate /etc/ssl/certs/biitbot.crt;
    ssl_certificate_key /etc/ssl/private/biitbot.key;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=webhook:10m rate=100r/s;

    # API endpoints
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://biitbot_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Webhook endpoints (higher rate limit)
    location /webhooks/ {
        limit_req zone=webhook burst=200 nodelay;
        proxy_pass http://biitbot_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Health check
    location /health {
        proxy_pass http://biitbot_api;
        access_log off;
    }
}
```

## 📊 Monitoring and Logging

### Winston Logger Configuration
```javascript
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'facebook-integration' },
  transports: [
    new winston.transports.File({ 
      filename: 'logs/error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'logs/combined.log' 
    })
  ]
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

module.exports = logger;
```

### Health Check Endpoint
```javascript
app.get('/health', async (req, res) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version,
    checks: {}
  };

  try {
    // Database check
    await db.query('SELECT 1');
    health.checks.database = 'ok';
  } catch (error) {
    health.checks.database = 'error';
    health.status = 'error';
  }

  try {
    // Redis check
    await redis.ping();
    health.checks.redis = 'ok';
  } catch (error) {
    health.checks.redis = 'error';
    health.status = 'warning';
  }

  // Facebook API check
  try {
    const response = await fetch(`https://graph.facebook.com/${process.env.FACEBOOK_API_VERSION}/me?access_token=${process.env.FACEBOOK_CLIENT_ID}|${process.env.FACEBOOK_CLIENT_SECRET}`);
    health.checks.facebook_api = response.ok ? 'ok' : 'error';
  } catch (error) {
    health.checks.facebook_api = 'error';
  }

  const statusCode = health.status === 'ok' ? 200 : 503;
  res.status(statusCode).json(health);
});
```

## 🔒 Security Configuration

### Helmet.js Security Headers
```javascript
const helmet = require('helmet');

app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://graph.facebook.com"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false
}));
```

### CORS Configuration
```javascript
const cors = require('cors');

const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      'https://app.biitbot.com',
      'https://caiman-legal-treefrog.ngrok-free.app'
    ];
    
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));
```

This configuration provides a complete deployment setup for the Facebook/Instagram backend integration.
