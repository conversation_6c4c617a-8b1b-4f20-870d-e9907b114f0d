# Backend Integration Guides

## 📋 Overview

This directory contains comprehensive guides for implementing backend integrations for BiitBot's social media platforms. These guides are designed to be used by AI assistants and developers to implement robust, secure, and scalable integrations.

## 📁 Available Guides

### 🔵 Facebook/Instagram Integration
Complete backend implementation guide for Facebook and Instagram Business API integration.

#### Files:
- **`facebook-integration.md`** - Main implementation guide
- **`facebook-deployment-config.md`** - Deployment and configuration
- **`facebook-testing-examples.md`** - Testing strategies and examples

#### What's Included:
- ✅ Complete API endpoint implementations
- ✅ Database schema and migrations
- ✅ OAuth 2.0 flow implementation
- ✅ Instagram Business API integration
- ✅ Webhook processing
- ✅ Security best practices
- ✅ Error handling and validation
- ✅ Testing strategies
- ✅ Deployment configuration
- ✅ Monitoring and logging

## 🎯 Frontend Integration Status

### ✅ Completed Frontend Implementation
The frontend for Facebook/Instagram integration is **100% complete** and includes:

- **React Components**: Modern, accessible UI components
- **Custom Hooks**: `useFacebookAuth` for state management
- **Validation Utilities**: Input sanitization and validation
- **Error Handling**: Specific error types with user-friendly messages
- **Confirmation Modals**: For destructive actions
- **Connection Status**: Visual indicators for account status
- **Internationalization**: English and Spanish translations
- **Documentation**: Complete technical documentation

### 🔄 Backend Implementation Needed
The backend implementation is **pending** and requires:

1. **API Endpoints**: All endpoints defined in the guides
2. **Database Setup**: Tables and migrations
3. **OAuth Flow**: Complete authentication flow
4. **Webhook Processing**: Instagram message handling
5. **Security Implementation**: Input validation and rate limiting

## 🚀 Quick Start for Backend Implementation

### 1. Prerequisites
```bash
# Required environment variables
FACEBOOK_CLIENT_ID=****************
FACEBOOK_CLIENT_SECRET=your_secret_here
FACEBOOK_API_VERSION=v23.0
```

### 2. Database Setup
```sql
-- Run migrations from facebook-deployment-config.md
psql -d biitbot -f migrations/001_create_social_auth_tables.sql
```

### 3. Install Dependencies
```bash
npm install express cors helmet rate-limiter-flexible
npm install --save-dev jest supertest nock
```

### 4. Implement Core Endpoints
Follow the implementation guide in `facebook-integration.md`:
- OAuth initiation and callback
- Account management endpoints
- Webhook processing
- Token management

## 📊 Expected Frontend API Calls

The frontend makes these API calls that need backend implementation:

### Authentication Flow
```javascript
// 1. Get OAuth URL
GET /social-auth/facebook/:botRequestId/auth-url

// 2. Handle OAuth callback
GET /social-auth/facebook/callback?code=...&state=...
```

### Account Management
```javascript
// 3. Get user integrations
GET /social-auth/botCompany

// 4. Select active account
PUT /social-auth/integration/select
Body: { socialAuthId, botRequestId }

// 5. Unlink account
PUT /social-auth/integration/:id/unlink
```

### Additional Endpoints (Optional)
```javascript
// Token refresh
POST /social-auth/facebook/refresh-token/:id

// Account validation
GET /social-auth/facebook/validate/:id

// Account info
GET /social-auth/facebook/account-info/:id
```

## 🔧 Implementation Priority

### Phase 1: Core OAuth (HIGH PRIORITY)
- [ ] OAuth URL generation
- [ ] OAuth callback handling
- [ ] Database storage
- [ ] Basic account listing

### Phase 2: Account Management (MEDIUM PRIORITY)
- [ ] Account selection
- [ ] Account unlinking
- [ ] Connection status checking

### Phase 3: Instagram Integration (MEDIUM PRIORITY)
- [ ] Webhook setup
- [ ] Message processing
- [ ] Real-time updates

### Phase 4: Advanced Features (LOW PRIORITY)
- [ ] Token refresh
- [ ] Account validation
- [ ] Monitoring and analytics

## 🔒 Security Checklist

### Required Security Implementations
- [ ] Input validation for all endpoints
- [ ] OAuth state parameter validation
- [ ] Webhook signature verification
- [ ] Rate limiting implementation
- [ ] CORS configuration
- [ ] SQL injection prevention
- [ ] XSS protection headers
- [ ] Token encryption in database

## 🧪 Testing Strategy

### Test Coverage Requirements
- [ ] Unit tests for all service functions
- [ ] Integration tests for OAuth flow
- [ ] Webhook processing tests
- [ ] Error handling tests
- [ ] Security validation tests

### Test Files to Create
```
tests/
├── unit/
│   ├── facebook-auth.test.js
│   ├── instagram-api.test.js
│   └── validation.test.js
├── integration/
│   ├── oauth-flow.test.js
│   └── webhook-processing.test.js
└── e2e/
    └── complete-flow.test.js
```

## 📈 Success Metrics

### Technical Metrics
- [ ] All API endpoints respond correctly
- [ ] OAuth flow completes successfully
- [ ] Webhooks process messages
- [ ] Error handling works properly
- [ ] Security measures are active

### User Experience Metrics
- [ ] Frontend connects without errors
- [ ] Account selection works
- [ ] Unlinking works properly
- [ ] Error messages are helpful
- [ ] Performance is acceptable (<2s response)

## 🔄 Integration with Existing Systems

### Database Integration
- Extends existing `companies` table
- Integrates with existing `messages` table
- Uses existing user authentication

### WebSocket Integration
- Emits events to existing Socket.io setup
- Uses existing room structure
- Maintains existing event patterns

### API Structure
- Follows existing REST API patterns
- Uses existing authentication middleware
- Maintains existing error response format

## 📞 Support and Troubleshooting

### Common Issues
1. **OAuth Callback URL Mismatch**: Ensure callback URLs match Facebook App settings
2. **Token Expiration**: Implement proper token refresh mechanism
3. **Webhook Verification**: Verify webhook signatures properly
4. **Rate Limiting**: Handle Facebook API rate limits

### Debug Tools
- Use Facebook Graph API Explorer for testing
- Check webhook logs for message processing
- Monitor database for account storage
- Use frontend network tab for API debugging

## 📚 Additional Resources

### Facebook Documentation
- [Facebook Graph API](https://developers.facebook.com/docs/graph-api/)
- [Instagram Business API](https://developers.facebook.com/docs/instagram-api/)
- [Webhooks](https://developers.facebook.com/docs/graph-api/webhooks/)

### Implementation Examples
- See `facebook-testing-examples.md` for test patterns
- See `facebook-deployment-config.md` for production setup
- See `facebook-integration.md` for complete implementation

---

**Ready to implement? Start with `facebook-integration.md` for the complete technical guide!** 🚀
