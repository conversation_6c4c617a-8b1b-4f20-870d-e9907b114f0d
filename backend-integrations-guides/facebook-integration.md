# Facebook/Instagram Backend Integration Guide

## 📋 Overview

This guide provides complete backend implementation details for Facebook/Instagram SaaS integration in BiitBot. The frontend is already implemented and expects specific API endpoints and data structures.

## 🏗️ Architecture Overview

### Integration Flow
```
User → Frontend → Backend API → Facebook Graph API → Instagram Business API
                     ↓
              Database Storage
                     ↓
              WebSocket Updates
```

### Key Components
1. **OAuth Authentication Controller**
2. **Facebook Graph API Service**
3. **Instagram Business API Service**
4. **Social Auth Database Models**
5. **WebSocket Event Handlers**
6. **Token Management System**

## 🔧 Required Environment Variables

```bash
# Facebook App Configuration
FACEBOOK_CLIENT_ID=****************
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret
FACEBOOK_API_VERSION=v23.0

# Callback URLs (Environment Specific)
FACEBOOK_CALLBACK_URL_DEV=https://caiman-legal-treefrog.ngrok-free.app/social-auth/facebook/callback
FACEBOOK_CALLBACK_URL_PROD=https://app.biitbot.com/social-auth/facebook/callback

# Instagram Business API
INSTAGRAM_BUSINESS_API_URL=https://graph.facebook.com/v23.0
```

## 📊 Database Schema

### SocialAuthAccount Table
```sql
CREATE TABLE social_auth_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id),
    provider VARCHAR(50) NOT NULL, -- 'facebook', 'instagram'
    provider_account_id VARCHAR(255) NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    account_id VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'chat', 'page'
    access_token TEXT NOT NULL,
    refresh_token TEXT,
    token_expires_at TIMESTAMP,
    scopes TEXT[], -- Array of granted permissions
    account_data JSONB, -- Store additional account info
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(provider, provider_account_id, company_id)
);
```

### SelectedBotCompany Table
```sql
CREATE TABLE selected_bot_companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    social_auth_account_id UUID NOT NULL REFERENCES social_auth_accounts(id),
    bot_request_id VARCHAR(255) NOT NULL,
    company_id UUID NOT NULL REFERENCES companies(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(bot_request_id, company_id)
);
```

## 🛠️ API Endpoints Implementation

### 1. OAuth Initiation
```javascript
// GET /social-auth/facebook/:botRequestId/auth-url
app.get('/social-auth/facebook/:botRequestId/auth-url', async (req, res) => {
  try {
    const { botRequestId } = req.params;
    const { companyId } = req.user; // From JWT token
    
    // Validate botRequestId
    if (!botRequestId || !/^[a-zA-Z0-9-_]+$/.test(botRequestId)) {
      return res.status(400).json({
        error: 'Invalid botRequestId format'
      });
    }
    
    const scopes = [
      'instagram_business_basic',
      'instagram_business_manage_messages',
      'pages_show_list',
      'instagram_manage_messages',
      'instagram_basic',
      'human_agent'
    ];
    
    const params = new URLSearchParams({
      client_id: process.env.FACEBOOK_CLIENT_ID,
      display: 'page',
      extras: JSON.stringify({ setup: { channel: 'IG_API_ONBOARDING' } }),
      redirect_uri: getCallbackUrl(),
      response_type: 'code',
      scope: scopes.join(','),
      state: `${botRequestId}:${companyId}` // Include company for security
    });
    
    const authUrl = `https://www.facebook.com/${process.env.FACEBOOK_API_VERSION}/dialog/oauth?${params.toString()}`;
    
    res.json({ authUrl });
  } catch (error) {
    console.error('Facebook auth URL generation error:', error);
    res.status(500).json({ error: 'Failed to generate auth URL' });
  }
});
```

### 2. OAuth Callback Handler
```javascript
// GET /social-auth/facebook/callback
app.get('/social-auth/facebook/callback', async (req, res) => {
  try {
    const { code, state, error, error_description } = req.query;
    
    // Handle OAuth errors
    if (error) {
      console.error('Facebook OAuth error:', error, error_description);
      return res.redirect(`${getFrontendUrl()}/integrations?error=${encodeURIComponent(error)}`);
    }
    
    // Validate state parameter
    if (!state) {
      return res.redirect(`${getFrontendUrl()}/integrations?error=missing_state`);
    }
    
    const [botRequestId, companyId] = state.split(':');
    if (!botRequestId || !companyId) {
      return res.redirect(`${getFrontendUrl()}/integrations?error=invalid_state`);
    }
    
    // Exchange code for access token
    const tokenData = await exchangeCodeForToken(code);
    
    // Get user's Facebook pages and Instagram accounts
    const accounts = await getFacebookPagesAndInstagramAccounts(tokenData.access_token);
    
    // Store accounts in database
    await storeAccountsInDatabase(accounts, companyId, tokenData);
    
    // Redirect to success page
    res.redirect(`${getFrontendUrl()}/integrations?success=true&botRequestId=${botRequestId}`);
    
  } catch (error) {
    console.error('Facebook callback error:', error);
    res.redirect(`${getFrontendUrl()}/integrations?error=callback_failed`);
  }
});
```

### 3. Get Bot Company Integrations
```javascript
// GET /social-auth/botCompany
app.get('/social-auth/botCompany', authenticateToken, async (req, res) => {
  try {
    const { companyId } = req.user;
    
    const accounts = await db.query(`
      SELECT 
        sa.*,
        sbc.bot_request_id,
        sbc.id as selected_bot_company_id
      FROM social_auth_accounts sa
      LEFT JOIN selected_bot_companies sbc ON sa.id = sbc.social_auth_account_id
      WHERE sa.company_id = $1 
        AND sa.provider IN ('facebook', 'instagram')
        AND sa.is_active = true
      ORDER BY sa.created_at DESC
    `, [companyId]);
    
    const response = {
      socialAuthAccount: accounts.rows.map(account => ({
        id: account.id,
        accountName: account.account_name,
        accountId: account.account_id,
        type: account.type,
        provider: account.provider,
        isActive: account.is_active,
        createdAt: account.created_at,
        selectedBotCompany: account.bot_request_id ? {
          id: account.selected_bot_company_id,
          botRequestId: account.bot_request_id
        } : null,
        // Add connection status
        connectionStatus: await getAccountConnectionStatus(account),
        lastSync: account.account_data?.lastSync,
        messageCount: account.account_data?.messageCount || 0
      }))
    };
    
    res.json(response);
  } catch (error) {
    console.error('Get integrations error:', error);
    res.status(500).json({ error: 'Failed to fetch integrations' });
  }
});
```

### 4. Update Selected Social Auth
```javascript
// PUT /social-auth/integration/select
app.put('/social-auth/integration/select', authenticateToken, async (req, res) => {
  try {
    const { socialAuthId, botRequestId } = req.body;
    const { companyId } = req.user;
    
    // Validate input
    if (!socialAuthId || !botRequestId) {
      return res.status(400).json({ error: 'socialAuthId and botRequestId are required' });
    }
    
    // Verify account belongs to company
    const account = await db.query(
      'SELECT id FROM social_auth_accounts WHERE id = $1 AND company_id = $2',
      [socialAuthId, companyId]
    );
    
    if (account.rows.length === 0) {
      return res.status(404).json({ error: 'Account not found' });
    }
    
    // Remove existing selection for this bot
    await db.query(
      'DELETE FROM selected_bot_companies WHERE bot_request_id = $1 AND company_id = $2',
      [botRequestId, companyId]
    );
    
    // Create new selection
    await db.query(`
      INSERT INTO selected_bot_companies (social_auth_account_id, bot_request_id, company_id)
      VALUES ($1, $2, $3)
    `, [socialAuthId, botRequestId, companyId]);
    
    res.json({ success: true, message: 'Selection updated successfully' });
  } catch (error) {
    console.error('Update selection error:', error);
    res.status(500).json({ error: 'Failed to update selection' });
  }
});
```

### 5. Unlink Integration
```javascript
// PUT /social-auth/integration/:id/unlink
app.put('/social-auth/integration/:id/unlink', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { companyId } = req.user;
    
    // Get account details for cleanup
    const account = await db.query(
      'SELECT * FROM social_auth_accounts WHERE id = $1 AND company_id = $2',
      [id, companyId]
    );
    
    if (account.rows.length === 0) {
      return res.status(404).json({ error: 'Account not found' });
    }
    
    const accountData = account.rows[0];
    
    // Revoke Facebook token (optional but recommended)
    try {
      await revokeFacebookToken(accountData.access_token);
    } catch (error) {
      console.warn('Failed to revoke Facebook token:', error);
    }
    
    // Remove from selected bot companies
    await db.query(
      'DELETE FROM selected_bot_companies WHERE social_auth_account_id = $1',
      [id]
    );
    
    // Soft delete the account
    await db.query(
      'UPDATE social_auth_accounts SET is_active = false, updated_at = NOW() WHERE id = $1',
      [id]
    );
    
    res.json({ success: true, message: 'Integration unlinked successfully' });
  } catch (error) {
    console.error('Unlink integration error:', error);
    res.status(500).json({ error: 'Failed to unlink integration' });
  }
});
```

## 🔧 Facebook Graph API Service Functions

### Token Exchange
```javascript
async function exchangeCodeForToken(code) {
  const params = new URLSearchParams({
    client_id: process.env.FACEBOOK_CLIENT_ID,
    client_secret: process.env.FACEBOOK_CLIENT_SECRET,
    redirect_uri: getCallbackUrl(),
    code: code
  });
  
  const response = await fetch(`https://graph.facebook.com/${process.env.FACEBOOK_API_VERSION}/oauth/access_token`, {
    method: 'POST',
    body: params
  });
  
  if (!response.ok) {
    throw new Error(`Token exchange failed: ${response.statusText}`);
  }
  
  return await response.json();
}
```

### Get Facebook Pages and Instagram Accounts
```javascript
async function getFacebookPagesAndInstagramAccounts(accessToken) {
  const accounts = [];
  
  // Get Facebook Pages
  const pagesResponse = await fetch(
    `https://graph.facebook.com/${process.env.FACEBOOK_API_VERSION}/me/accounts?access_token=${accessToken}`
  );
  const pagesData = await pagesResponse.json();
  
  for (const page of pagesData.data) {
    // Add Facebook page
    accounts.push({
      provider: 'facebook',
      providerAccountId: page.id,
      accountName: page.name,
      accountId: page.id,
      type: 'page',
      accessToken: page.access_token,
      scopes: page.tasks || []
    });
    
    // Check for connected Instagram account
    try {
      const igResponse = await fetch(
        `https://graph.facebook.com/${process.env.FACEBOOK_API_VERSION}/${page.id}?fields=instagram_business_account&access_token=${page.access_token}`
      );
      const igData = await igResponse.json();
      
      if (igData.instagram_business_account) {
        const igAccountId = igData.instagram_business_account.id;
        
        // Get Instagram account details
        const igDetailsResponse = await fetch(
          `https://graph.facebook.com/${process.env.FACEBOOK_API_VERSION}/${igAccountId}?fields=name,username,profile_picture_url&access_token=${page.access_token}`
        );
        const igDetails = await igDetailsResponse.json();
        
        accounts.push({
          provider: 'instagram',
          providerAccountId: igAccountId,
          accountName: igDetails.name || igDetails.username,
          accountId: igDetails.username,
          type: 'chat',
          accessToken: page.access_token,
          scopes: ['instagram_business_basic', 'instagram_business_manage_messages'],
          accountData: {
            profilePictureUrl: igDetails.profile_picture_url,
            connectedPageId: page.id
          }
        });
      }
    } catch (error) {
      console.warn(`Failed to get Instagram account for page ${page.id}:`, error);
    }
  }
  
  return accounts;
}
```

## 📝 Additional Implementation Notes

### Error Handling
- Implement circuit breaker pattern for Facebook API calls
- Add retry logic with exponential backoff
- Log all API errors for debugging
- Return user-friendly error messages

### Security Considerations
- Validate all input parameters
- Use HTTPS for all communications
- Store tokens encrypted in database
- Implement rate limiting
- Validate state parameter in OAuth flow

### Token Management
- Implement token refresh mechanism
- Monitor token expiration
- Handle token revocation gracefully
- Store token metadata (scopes, expiration)

### WebSocket Integration
- Emit events when accounts are connected/disconnected
- Real-time updates for message status
- Connection status changes

### Testing
- Unit tests for all service functions
- Integration tests for OAuth flow
- Mock Facebook API responses
- Test error scenarios

## 🔄 Instagram Business API Integration

### Webhook Configuration
```javascript
// POST /webhooks/instagram
app.post('/webhooks/instagram', (req, res) => {
  const body = req.body;

  // Verify webhook signature
  if (!verifyWebhookSignature(req)) {
    return res.status(401).send('Unauthorized');
  }

  // Process Instagram webhook events
  if (body.object === 'instagram') {
    body.entry.forEach(entry => {
      entry.messaging?.forEach(async (messagingEvent) => {
        await processInstagramMessage(messagingEvent);
      });
    });
  }

  res.status(200).send('EVENT_RECEIVED');
});

// GET /webhooks/instagram (Verification)
app.get('/webhooks/instagram', (req, res) => {
  const mode = req.query['hub.mode'];
  const token = req.query['hub.verify_token'];
  const challenge = req.query['hub.challenge'];

  if (mode === 'subscribe' && token === process.env.INSTAGRAM_WEBHOOK_VERIFY_TOKEN) {
    res.status(200).send(challenge);
  } else {
    res.status(403).send('Forbidden');
  }
});
```

### Message Processing
```javascript
async function processInstagramMessage(messagingEvent) {
  const { sender, recipient, message, timestamp } = messagingEvent;

  try {
    // Find the Instagram account in our database
    const account = await db.query(
      'SELECT * FROM social_auth_accounts WHERE provider_account_id = $1 AND provider = $2',
      [recipient.id, 'instagram']
    );

    if (account.rows.length === 0) {
      console.warn('Received message for unknown Instagram account:', recipient.id);
      return;
    }

    const accountData = account.rows[0];

    // Get sender information
    const senderInfo = await getInstagramUserInfo(sender.id, accountData.access_token);

    // Store message in database
    const messageData = {
      externalId: message.mid,
      senderId: sender.id,
      senderName: senderInfo.name || senderInfo.username,
      recipientId: recipient.id,
      content: message.text || '',
      messageType: getMessageType(message),
      timestamp: new Date(timestamp),
      accountId: accountData.id,
      companyId: accountData.company_id,
      platform: 'instagram'
    };

    await storeMessage(messageData);

    // Emit real-time event
    io.to(`company_${accountData.company_id}`).emit('new_message', {
      ...messageData,
      accountName: accountData.account_name
    });

    // Process with AI bot if configured
    await processBotResponse(messageData, accountData);

  } catch (error) {
    console.error('Error processing Instagram message:', error);
  }
}
```

## 🔧 Additional Service Functions

### Account Connection Status
```javascript
async function getAccountConnectionStatus(account) {
  try {
    // Test API connection
    const response = await fetch(
      `https://graph.facebook.com/${process.env.FACEBOOK_API_VERSION}/${account.provider_account_id}?access_token=${account.access_token}`
    );

    if (response.ok) {
      return 'connected';
    } else if (response.status === 401) {
      return 'error'; // Token expired or invalid
    } else {
      return 'warning'; // Other issues
    }
  } catch (error) {
    return 'error';
  }
}
```

### Token Refresh
```javascript
async function refreshFacebookToken(accountId) {
  const account = await db.query(
    'SELECT * FROM social_auth_accounts WHERE id = $1',
    [accountId]
  );

  if (account.rows.length === 0) {
    throw new Error('Account not found');
  }

  const accountData = account.rows[0];

  // Facebook long-lived tokens don't need refresh, but we can extend them
  const response = await fetch(
    `https://graph.facebook.com/${process.env.FACEBOOK_API_VERSION}/oauth/access_token?grant_type=fb_exchange_token&client_id=${process.env.FACEBOOK_CLIENT_ID}&client_secret=${process.env.FACEBOOK_CLIENT_SECRET}&fb_exchange_token=${accountData.access_token}`
  );

  if (!response.ok) {
    throw new Error('Token refresh failed');
  }

  const tokenData = await response.json();

  // Update token in database
  await db.query(
    'UPDATE social_auth_accounts SET access_token = $1, token_expires_at = $2, updated_at = NOW() WHERE id = $3',
    [tokenData.access_token, new Date(Date.now() + tokenData.expires_in * 1000), accountId]
  );

  return tokenData;
}
```

## 📊 Frontend Integration Points

### Expected API Responses

The frontend expects these exact response formats:

#### GET /social-auth/botCompany
```json
{
  "socialAuthAccount": [
    {
      "id": "uuid",
      "accountName": "Instagram Account Name",
      "accountId": "@username",
      "type": "chat",
      "provider": "instagram",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00Z",
      "selectedBotCompany": {
        "id": "uuid",
        "botRequestId": "bot-123"
      } | null,
      "connectionStatus": "connected" | "error" | "warning",
      "lastSync": "2024-01-01T00:00:00Z",
      "messageCount": 42
    }
  ]
}
```

#### Error Response Format
```json
{
  "error": "User-friendly error message",
  "code": "ERROR_CODE",
  "details": ["Detailed error info for debugging"]
}
```

## 🔒 Security Implementation

### Input Validation Middleware
```javascript
const validateBotRequestId = (req, res, next) => {
  const { botRequestId } = req.params;

  if (!botRequestId || !/^[a-zA-Z0-9-_]+$/.test(botRequestId)) {
    return res.status(400).json({
      error: 'Invalid botRequestId format',
      code: 'INVALID_BOT_REQUEST_ID'
    });
  }

  next();
};
```

### Rate Limiting
```javascript
const rateLimit = require('express-rate-limit');

const facebookApiLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests, please try again later',
    code: 'RATE_LIMIT_EXCEEDED'
  }
});

app.use('/social-auth/facebook', facebookApiLimit);
```

## 🧪 Testing Implementation

### Unit Test Example
```javascript
describe('Facebook Integration', () => {
  test('should exchange code for token', async () => {
    const mockCode = 'test_code';
    const mockToken = 'test_token';

    // Mock Facebook API response
    nock('https://graph.facebook.com')
      .post('/v23.0/oauth/access_token')
      .reply(200, { access_token: mockToken });

    const result = await exchangeCodeForToken(mockCode);
    expect(result.access_token).toBe(mockToken);
  });
});
```

## 📋 Deployment Checklist

### Environment Setup
- [ ] Configure Facebook App ID and Secret
- [ ] Set up webhook endpoints
- [ ] Configure callback URLs
- [ ] Set up database tables
- [ ] Configure rate limiting
- [ ] Set up monitoring and logging

### Security Checklist
- [ ] Validate all input parameters
- [ ] Implement CSRF protection
- [ ] Use HTTPS for all endpoints
- [ ] Encrypt tokens in database
- [ ] Implement proper error handling
- [ ] Set up webhook signature verification

This guide provides the complete backend implementation needed to support the existing frontend Facebook/Instagram integration.
